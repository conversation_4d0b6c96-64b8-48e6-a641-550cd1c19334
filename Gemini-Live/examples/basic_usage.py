"""
Basic usage examples for the Gemini Live Computer Control Agent.
"""

import asyncio
import sys
import os

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.gemini_client import gemini_client
from core.screen_analyzer import screen_analyzer
from core.action_executor import action_executor
from core.command_processor import command_processor
from utils.logger import logger


async def example_direct_actions():
    """Example of executing direct actions without AI analysis."""
    print("=== Direct Actions Example ===")
    
    # Example actions
    actions = [
        {
            "type": "move",
            "coordinates": [500, 300],
            "description": "Move mouse to center"
        },
        {
            "type": "wait",
            "duration": 1.0,
            "description": "Wait 1 second"
        },
        {
            "type": "click",
            "coordinates": [500, 300],
            "description": "Click at center"
        }
    ]
    
    print(f"Executing {len(actions)} direct actions...")
    
    for action in actions:
        print(f"Executing: {action['description']}")
        success = action_executor.execute_action(action)
        print(f"Result: {'Success' if success else 'Failed'}")
        
        if not success:
            break


async def example_screen_analysis():
    """Example of screen analysis and AI-driven actions."""
    print("\n=== Screen Analysis Example ===")
    
    # Initialize Gemini client
    if not await gemini_client.connect():
        print("Failed to connect to Gemini API")
        return
    
    # Capture screenshot
    print("Capturing screenshot...")
    screenshot = screen_analyzer.capture_screenshot()
    image_base64 = screen_analyzer.screenshot_to_base64(screenshot)
    screen_info = screen_analyzer.get_screen_info()
    
    print(f"Screen size: {screen_info['screen_width']}x{screen_info['screen_height']}")
    print(f"Mouse position: ({screen_info['mouse_x']}, {screen_info['mouse_y']})")
    
    # Send to Gemini for analysis
    user_command = "Move the mouse to the top-left corner of the screen"
    print(f"Sending command to AI: {user_command}")
    
    response = await gemini_client.send_screen_analysis_request(
        image_base64, user_command, screen_info
    )
    
    if response:
        print("AI Response received:")
        if 'explanation' in response:
            print(f"Explanation: {response['explanation']}")
        
        # Validate and execute actions
        is_valid, error_msg = command_processor.validate_ai_response(response)
        
        if is_valid:
            actions = command_processor.extract_action_sequence(response)
            print(f"Executing {len(actions)} AI-suggested actions...")
            
            for action in actions:
                success = action_executor.execute_action(action)
                print(f"Action {action['type']}: {'Success' if success else 'Failed'}")
        else:
            print(f"Invalid AI response: {error_msg}")
    else:
        print("Failed to get response from AI")


async def example_command_processing():
    """Example of command processing and interpretation."""
    print("\n=== Command Processing Example ===")
    
    test_commands = [
        "click 100,200",
        "double click on the button",
        "type 'Hello World'",
        "press ctrl+c",
        "move mouse to 300,400",
        "scroll down 3 times",
        "wait for 2 seconds",
        "find the save button and click it"
    ]
    
    for command in test_commands:
        print(f"\nProcessing command: '{command}'")
        processed = command_processor.process_user_command(command)
        
        print(f"  Type: {processed.command_type.value}")
        print(f"  Intent: {processed.intent}")
        print(f"  Parameters: {processed.parameters}")
        print(f"  Requires screen analysis: {processed.requires_screen_analysis}")
        print(f"  Confidence: {processed.confidence}")


async def example_safety_features():
    """Example of safety features and validation."""
    print("\n=== Safety Features Example ===")
    
    from utils.safety import safety_manager
    
    # Test safety checks
    test_actions = [
        ("click", (100, 100)),
        ("click", (0, 0)),  # Might be in restricted area
        ("type", None),
        ("key_combination", None)
    ]
    
    for action_type, coordinates in test_actions:
        is_safe, reason = safety_manager.is_action_safe(action_type, coordinates)
        print(f"Action '{action_type}' at {coordinates}: {'SAFE' if is_safe else 'BLOCKED'} - {reason}")
    
    # Test action validation
    test_responses = [
        {
            "actions": [
                {"type": "click", "coordinates": [100, 200]}
            ],
            "explanation": "Click at coordinates"
        },
        {
            "actions": [
                {"type": "invalid_action", "coordinates": [100, 200]}
            ]
        },
        {
            "actions": [
                {"type": "type", "text": "Hello"}
            ]
        }
    ]
    
    print("\nValidating AI responses:")
    for i, response in enumerate(test_responses, 1):
        is_valid, error_msg = command_processor.validate_ai_response(response)
        print(f"Response {i}: {'VALID' if is_valid else 'INVALID'} - {error_msg}")


async def example_screen_capture():
    """Example of screen capture and image processing."""
    print("\n=== Screen Capture Example ===")
    
    # Capture full screen
    screenshot = screen_analyzer.capture_screenshot()
    print(f"Full screenshot: {screenshot.width}x{screenshot.height}")
    
    # Capture region
    region_screenshot = screen_analyzer.capture_screenshot(region=(0, 0, 500, 500))
    print(f"Region screenshot: {region_screenshot.width}x{region_screenshot.height}")
    
    # Get screen info
    screen_info = screen_analyzer.get_screen_info()
    print(f"Screen info: {screen_info}")
    
    # Convert to base64
    base64_data = screen_analyzer.screenshot_to_base64(screenshot)
    print(f"Base64 encoded size: {len(base64_data)} characters")
    
    # Get pixel color
    color = screen_analyzer.get_pixel_color(100, 100)
    print(f"Pixel color at (100, 100): RGB{color}")


async def main():
    """Run all examples."""
    print("Gemini Live Computer Control Agent - Examples")
    print("=" * 50)
    
    try:
        # Run examples
        await example_command_processing()
        await example_safety_features()
        await example_screen_capture()
        
        # These examples require user interaction
        print("\n" + "=" * 50)
        response = input("Run interactive examples? (y/N): ")
        
        if response.lower() in ['y', 'yes']:
            await example_direct_actions()
            await example_screen_analysis()
        
        print("\nExamples completed!")
        
    except Exception as e:
        logger.error(f"Error running examples: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
