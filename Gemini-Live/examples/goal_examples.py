"""Example goals and usage patterns for the enhanced system."""

import asyncio
from ..enhanced_command_interpreter import EnhancedCommandInterpreter


# Example goals of varying complexity
EXAMPLE_GOALS = {
    "simple": [
        "Open calculator and compute 15 * 23",
        "Take a screenshot and save it to desktop",
        "Open a text editor and type 'Hello World'",
        "Click on the time in the system tray",
        "Open the start menu and close it"
    ],
    
    "medium": [
        "Open a text editor and write a Python function to calculate fibonacci numbers",
        "Find and open a PDF file, then scroll to page 5", 
        "Create a new folder on desktop and move all image files into it",
        "Open a spreadsheet and create a simple budget with 5 categories",
        "Search for 'machine learning' in a web browser and bookmark the first result"
    ],
    
    "complex": [
        "Research machine learning papers online and create a summary document",
        "Organize my desktop files by type into appropriate folders with proper naming",
        "Open a spreadsheet, create a budget template with categories and formulas, then save it",
        "Find all PDF files on desktop, rename them with today's date prefix, and organize by size",
        "Create a presentation with 3 slides about renewable energy and save it"
    ]
}


async def run_simple_goal_example():
    """Example of running a simple goal."""
    interpreter = EnhancedCommandInterpreter()
    
    try:
        print("🎯 Running simple goal example...")
        
        goal = "Open calculator and compute 15 * 23"
        result = await interpreter.execute_goal(goal, dry_run=True)
        
        print(f"Goal: {goal}")
        print(f"Success: {result.success}")
        print(f"Execution time: {result.execution_time:.2f}s")
        print(f"Commands executed: {result.commands_executed}")
        
    finally:
        await interpreter.stop()


async def run_step_by_step_example():
    """Example of running a goal with step-by-step confirmation."""
    interpreter = EnhancedCommandInterpreter()
    
    try:
        print("👥 Running step-by-step goal example...")
        
        goal = "Create a new folder on desktop and move all image files into it"
        result = await interpreter.execute_goal(goal, step_by_step=True, dry_run=True)
        
        print(f"Goal: {goal}")
        print(f"Success: {result.success}")
        print(f"Message: {result.message}")
        
    finally:
        await interpreter.stop()


async def run_complex_goal_example():
    """Example of running a complex goal."""
    interpreter = EnhancedCommandInterpreter()
    
    try:
        print("🧠 Running complex goal example...")
        
        goal = "Research machine learning papers online and create a summary document"
        result = await interpreter.execute_goal(goal, dry_run=True)
        
        print(f"Goal: {goal}")
        print(f"Success: {result.success}")
        print(f"Execution time: {result.execution_time:.2f}s")
        
        # Show performance metrics
        perf_summary = interpreter.get_performance_summary()
        print(f"Performance recommendations: {len(perf_summary['recommendations'])}")
        
    finally:
        await interpreter.stop()


async def run_goal_management_example():
    """Example of goal management operations."""
    interpreter = EnhancedCommandInterpreter()
    
    try:
        print("📊 Running goal management example...")
        
        # Execute multiple goals
        goals = [
            "Open calculator and compute 10 + 5",
            "Take a screenshot",
            "Open notepad and type 'Test message'"
        ]
        
        for goal in goals:
            print(f"Executing: {goal}")
            await interpreter.execute_goal(goal, dry_run=True)
        
        # Check goal status
        status = interpreter.get_goal_status()
        print(f"Total goals processed: {status['summary']['total_goals']}")
        print(f"Success rate: {status['summary']['success_rate']:.1%}")
        
    finally:
        await interpreter.stop()


async def run_performance_monitoring_example():
    """Example of performance monitoring."""
    interpreter = EnhancedCommandInterpreter()
    
    try:
        print("📈 Running performance monitoring example...")
        
        # Start performance monitoring
        interpreter.performance_monitor.start_monitoring()
        
        # Execute some goals
        goals = EXAMPLE_GOALS["simple"][:3]
        
        for goal in goals:
            result = await interpreter.execute_goal(goal, dry_run=True)
            print(f"Executed: {goal} ({'✅' if result.success else '❌'})")
        
        # Get performance summary
        perf_data = interpreter.get_performance_summary()
        summary = perf_data["summary"]
        
        if "error" not in summary:
            print(f"Goals executed: {summary['goal_execution']['total_goals']}")
            print(f"Average execution time: {summary['goal_execution']['avg_execution_time']:.2f}s")
        
        # Get recommendations
        recommendations = perf_data["recommendations"]
        print(f"Optimization recommendations: {len(recommendations)}")
        for rec in recommendations[:2]:  # Show first 2
            print(f"  • {rec}")
        
    finally:
        await interpreter.stop()


async def run_visual_feedback_example():
    """Example of visual feedback system."""
    interpreter = EnhancedCommandInterpreter()
    
    try:
        print("🎨 Running visual feedback example...")
        
        # Start visual feedback
        interpreter.visual_feedback.start_live_display()
        
        # Execute a goal with visual tracking
        goal = "Open a text editor and write a Python function"
        
        print(f"Executing with visual feedback: {goal}")
        result = await interpreter.execute_goal(goal, dry_run=True)
        
        # Let the display run for a moment
        await asyncio.sleep(3)
        
        print(f"Goal completed: {'✅' if result.success else '❌'}")
        
    finally:
        await interpreter.stop()


async def run_error_recovery_example():
    """Example of error recovery and rollback."""
    interpreter = EnhancedCommandInterpreter()
    
    try:
        print("🔄 Running error recovery example...")
        
        # This would simulate a goal that encounters errors
        goal = "Attempt to open a non-existent application and handle the error"
        
        result = await interpreter.execute_goal(goal, dry_run=True)
        
        print(f"Goal: {goal}")
        print(f"Result: {result.message}")
        
        # In a real scenario, this would demonstrate:
        # - Task retry mechanisms
        # - Rollback operations
        # - Error recovery strategies
        
    finally:
        await interpreter.stop()


def print_example_goals():
    """Print all example goals organized by complexity."""
    print("📋 Example Goals by Complexity Level:\n")
    
    for complexity, goals in EXAMPLE_GOALS.items():
        print(f"🎯 {complexity.upper()} GOALS:")
        for i, goal in enumerate(goals, 1):
            print(f"  {i}. {goal}")
        print()


async def main():
    """Run all examples."""
    print("🚀 Enhanced Gemini Computer Control - Goal Examples\n")
    
    # Print available examples
    print_example_goals()
    
    # Run examples
    examples = [
        ("Simple Goal", run_simple_goal_example),
        ("Step-by-Step Goal", run_step_by_step_example),
        ("Complex Goal", run_complex_goal_example),
        ("Goal Management", run_goal_management_example),
        ("Performance Monitoring", run_performance_monitoring_example),
        ("Visual Feedback", run_visual_feedback_example),
        ("Error Recovery", run_error_recovery_example)
    ]
    
    for name, example_func in examples:
        print(f"\n{'='*50}")
        print(f"Running: {name}")
        print('='*50)
        
        try:
            await example_func()
        except Exception as e:
            print(f"❌ Example failed: {e}")
        
        print(f"✅ {name} completed")
        await asyncio.sleep(1)  # Brief pause between examples
    
    print(f"\n🎉 All examples completed!")


if __name__ == "__main__":
    asyncio.run(main())
