#!/usr/bin/env python3
"""Test script for the enhanced goal-oriented computer control system."""

import asyncio
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from enhanced_command_interpreter import EnhancedCommandInterpreter
from core.goal_processor import GoalProcessor
from core.data_models import Goal, GoalComplexity
from utils.visual_feedback import VisualFeedback


async def test_goal_processing():
    """Test goal processing capabilities."""
    print("🧠 Testing Goal Processing...")
    
    processor = GoalProcessor()
    
    test_goals = [
        "Open calculator and compute 15 * 23",
        "Create a new folder on desktop and organize image files",
        "Research machine learning papers and create a summary document"
    ]
    
    for goal_text in test_goals:
        print(f"\nProcessing: {goal_text}")
        
        # Parse goal
        goal = processor.parse_goal(goal_text)
        print(f"  Complexity: {goal.complexity.value}")
        print(f"  Sub-tasks: {len(goal.sub_tasks)}")
        print(f"  Estimated duration: {goal.estimated_duration:.1f}s")
        print(f"  Safety level: {goal.safety_level}")
        
        # Validate goal
        validation = processor.validate_goal(goal)
        print(f"  Valid: {validation.is_valid} (confidence: {validation.confidence:.2f})")
        
        if validation.warnings:
            print(f"  Warnings: {len(validation.warnings)}")
        if validation.safety_concerns:
            print(f"  Safety concerns: {len(validation.safety_concerns)}")
    
    print("✅ Goal processing test completed")


async def test_enhanced_interpreter():
    """Test the enhanced command interpreter."""
    print("\n🤖 Testing Enhanced Command Interpreter...")
    
    interpreter = EnhancedCommandInterpreter()
    
    try:
        # Test simple goal execution (dry run)
        goal = "Open calculator and compute 10 + 5"
        print(f"Executing goal (dry run): {goal}")
        
        result = await interpreter.execute_goal(goal, dry_run=True)
        
        print(f"  Success: {result.success}")
        print(f"  Message: {result.message}")
        print(f"  Execution time: {result.execution_time:.2f}s")
        print(f"  Commands executed: {result.commands_executed}")
        
    finally:
        await interpreter.stop()
    
    print("✅ Enhanced interpreter test completed")


async def test_visual_feedback():
    """Test visual feedback system."""
    print("\n🎨 Testing Visual Feedback System...")
    
    feedback = VisualFeedback()
    
    # Create a mock goal for testing
    goal = Goal(
        description="Test goal for visual feedback",
        complexity=GoalComplexity.SIMPLE,
        estimated_duration=30.0
    )
    
    # Test progress tracking
    feedback.start_goal_tracking(goal)
    
    for i in range(5):
        progress = (i + 1) / 5
        feedback.update_goal_progress(goal.id, progress, f"Step {i+1} completed")
        await asyncio.sleep(0.5)
    
    feedback.complete_goal_tracking(goal.id, True, "Test goal completed successfully")
    
    # Test decision logging
    feedback.add_decision_log("Test decision", "This is a test reasoning")
    feedback.add_execution_log("Test execution log entry")
    
    print("✅ Visual feedback test completed")


def test_data_models():
    """Test data models and serialization."""
    print("\n📊 Testing Data Models...")
    
    # Test Goal creation
    goal = Goal(
        description="Test goal",
        complexity=GoalComplexity.MEDIUM,
        estimated_duration=120.0
    )
    
    print(f"  Goal ID: {goal.id}")
    print(f"  Description: {goal.description}")
    print(f"  Complexity: {goal.complexity.value}")
    print(f"  Status: {goal.status.value}")
    
    # Test serialization
    try:
        goal_dict = goal.to_dict()
        restored_goal = Goal.from_dict(goal_dict)
        print(f"  Serialization: ✅ (ID preserved: {goal.id == restored_goal.id})")
    except Exception as e:
        print(f"  Serialization: ❌ ({e})")
    
    print("✅ Data models test completed")


def test_imports():
    """Test that all modules can be imported."""
    print("📦 Testing Module Imports...")
    
    modules_to_test = [
        ("core.goal_processor", "GoalProcessor"),
        ("core.task_planner", "TaskPlanner"),
        ("core.goal_state_manager", "GoalStateManager"),
        ("core.enhanced_perception", "EnhancedPerception"),
        ("core.performance_monitor", "PerformanceMonitor"),
        ("utils.visual_feedback", "VisualFeedback"),
        ("utils.goal_validators", "GoalValidator"),
        ("utils.ocr_utils", "OCRUtils")
    ]
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            cls = getattr(module, class_name)
            print(f"  ✅ {module_name}.{class_name}")
        except Exception as e:
            print(f"  ❌ {module_name}.{class_name}: {e}")
    
    print("✅ Import test completed")


async def test_performance_monitoring():
    """Test performance monitoring capabilities."""
    print("\n📈 Testing Performance Monitoring...")
    
    interpreter = EnhancedCommandInterpreter()
    
    try:
        # Start monitoring
        interpreter.performance_monitor.start_monitoring()
        
        # Take a performance snapshot
        snapshot = interpreter.performance_monitor.take_performance_snapshot(active_goals_count=1)
        print(f"  CPU: {snapshot.cpu_percent:.1f}%")
        print(f"  Memory: {snapshot.memory_mb:.1f}MB")
        print(f"  Commands/sec: {snapshot.commands_per_second:.2f}")
        
        # Get performance summary
        summary = interpreter.get_performance_summary()
        print(f"  Recommendations: {len(summary['recommendations'])}")
        
        interpreter.performance_monitor.stop_monitoring()
        
    finally:
        await interpreter.stop()
    
    print("✅ Performance monitoring test completed")


async def run_all_tests():
    """Run all tests."""
    print("🚀 Enhanced Gemini Computer Control - System Tests")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Data Models", test_data_models),
        ("Goal Processing", test_goal_processing),
        ("Enhanced Interpreter", test_enhanced_interpreter),
        ("Visual Feedback", test_visual_feedback),
        ("Performance Monitoring", test_performance_monitoring)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                await test_func()
            else:
                test_func()
            passed += 1
        except Exception as e:
            print(f"❌ Test failed: {e}")
            failed += 1
    
    print(f"\n{'='*60}")
    print(f"🎉 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("✅ All tests passed! The enhanced system is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return failed == 0


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
