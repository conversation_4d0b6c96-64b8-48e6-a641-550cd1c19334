# Gemini Live Computer Control Agent

An AI-powered software agent that can control a computer using mouse and keyboard interactions through the pyautogui library, powered by Google's Gemini Live API.

## Features

- **AI-Powered Control**: Uses Gemini 2.0 Flash Live model for intelligent computer interaction
- **Screen Analysis**: Captures and analyzes screenshots to understand the current state
- **Safe Execution**: Built-in safety mechanisms to prevent harmful actions
- **Natural Language Commands**: Accepts commands in natural language
- **Real-time Interaction**: Supports real-time communication with Gemini Live API
- **Comprehensive Logging**: Detailed logging for debugging and audit trails
- **Modular Architecture**: Clean, extensible codebase with separate concerns

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Input    │───▶│ Command         │───▶│ Screen          │
│                 │    │ Processor       │    │ Analyzer        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐           │
│ Action          │◀───│ Gemini Live     │◀──────────┘
│ Executor        │    │ Client          │
└─────────────────┘    └─────────────────┘
        │                       │
        ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│ Safety          │    │ Logger &        │
│ Manager         │    │ Configuration   │
└─────────────────┘    └─────────────────┘
```

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd Gemini-Live
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env and add your Gemini API key
   ```

4. **Get Gemini API Key**:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Add it to your `.env` file as `GEMINI_API_KEY=your_key_here`

## Usage

### Basic Usage

Run the agent in interactive mode:

```bash
python main.py
```

### Example Commands

**Direct Actions** (with specific coordinates):
```
click 100,200
double click 150,300
right click 200,400
move 250,500
type "Hello World"
press enter
press ctrl+c
```

**AI-Powered Actions** (using screen analysis):
```
click on the save button
open calculator
find the search box and type "python"
scroll down to see more content
close the current window
```

**System Commands**:
```
help          # Show available commands
status        # Show agent status
clear         # Clear conversation history
quit          # Exit the agent
```

### Programmatic Usage

```python
import asyncio
from core.gemini_client import gemini_client
from core.action_executor import action_executor

async def example():
    # Connect to Gemini
    await gemini_client.connect()
    
    # Execute a direct action
    action = {
        "type": "click",
        "coordinates": [100, 200]
    }
    success = action_executor.execute_action(action)
    
    # Disconnect
    await gemini_client.disconnect()

asyncio.run(example())
```

## Configuration

The agent can be configured through environment variables or the `config/settings.py` file:

### Safety Settings
- `ENABLE_SAFETY_CHECKS`: Enable/disable safety mechanisms
- `RESTRICTED_AREAS`: Screen areas where actions are forbidden
- `MAX_ACTIONS_PER_MINUTE`: Rate limiting for actions
- `REQUIRE_CONFIRMATION`: Require user confirmation for actions

### Performance Settings
- `SCREENSHOT_QUALITY`: JPEG quality for screenshots (1-100)
- `SCREENSHOT_INTERVAL`: Minimum time between screenshots
- `MAX_IMAGE_SIZE`: Maximum screenshot dimensions

### Logging Settings
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR)
- `LOG_FILE`: Path to log file
- `LOG_ACTIONS`: Enable action logging

## Safety Features

The agent includes several safety mechanisms:

1. **Rate Limiting**: Prevents excessive actions per minute
2. **Restricted Areas**: Blocks actions in specified screen regions
3. **Action Validation**: Validates all actions before execution
4. **User Confirmation**: Optional confirmation for sensitive actions
5. **Emergency Stop**: Failsafe mechanisms to halt execution
6. **Audit Logging**: Complete log of all actions taken

## API Reference

### Core Modules

#### `GeminiLiveClient`
- `connect()`: Connect to Gemini Live API
- `send_screen_analysis_request()`: Send screenshot and command for analysis
- `disconnect()`: Disconnect from API

#### `ScreenAnalyzer`
- `capture_screenshot()`: Capture screen or region
- `screenshot_to_base64()`: Convert image to base64
- `get_screen_info()`: Get screen dimensions and mouse position

#### `ActionExecutor`
- `execute_action()`: Execute a single action
- `execute_action_sequence()`: Execute multiple actions
- `get_statistics()`: Get execution statistics

#### `CommandProcessor`
- `process_user_command()`: Process natural language commands
- `validate_ai_response()`: Validate AI responses
- `extract_action_sequence()`: Extract actions from AI response

## Supported Actions

| Action Type | Description | Parameters |
|-------------|-------------|------------|
| `click` | Mouse click | `coordinates`, `button` |
| `double_click` | Mouse double-click | `coordinates` |
| `right_click` | Right mouse click | `coordinates` |
| `move` | Move mouse | `coordinates`, `duration` |
| `drag` | Drag mouse | `start_coordinates`, `end_coordinates` |
| `scroll` | Mouse scroll | `coordinates`, `clicks`, `direction` |
| `type` | Type text | `text`, `interval` |
| `key_press` | Press key | `key`, `presses` |
| `key_combination` | Key combination | `keys` |
| `wait` | Wait/pause | `duration` |

## Examples

See the `examples/basic_usage.py` file for comprehensive examples of:
- Direct action execution
- Screen analysis and AI-driven actions
- Command processing
- Safety feature demonstrations
- Screen capture functionality

## Development

### Project Structure

```
Gemini-Live/
├── main.py                    # Main entry point
├── requirements.txt           # Dependencies
├── .env.example              # Environment template
├── config/
│   └── settings.py           # Configuration management
├── core/
│   ├── gemini_client.py      # Gemini API client
│   ├── screen_analyzer.py    # Screen capture/analysis
│   ├── action_executor.py    # Action execution
│   └── command_processor.py  # Command processing
├── utils/
│   ├── logger.py            # Logging utilities
│   └── safety.py           # Safety mechanisms
└── examples/
    └── basic_usage.py       # Usage examples
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Troubleshooting

### Common Issues

1. **API Key Error**: Ensure your Gemini API key is correctly set in the `.env` file
2. **Permission Errors**: On macOS, you may need to grant accessibility permissions
3. **Screen Capture Issues**: Ensure the application has screen recording permissions
4. **Rate Limiting**: Adjust `MAX_ACTIONS_PER_MINUTE` if actions are being blocked

### Debugging

Enable debug logging by setting `LOG_LEVEL=DEBUG` in your `.env` file. This will provide detailed information about:
- API requests and responses
- Action execution details
- Safety check results
- Screen capture information

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This software is designed to automate computer interactions. Use responsibly and ensure you have appropriate permissions before running automated actions on any system. The developers are not responsible for any misuse or damage caused by this software.

## Acknowledgments

- Google Gemini team for the powerful AI model
- PyAutoGUI developers for the automation library
- Rich library for beautiful console output
