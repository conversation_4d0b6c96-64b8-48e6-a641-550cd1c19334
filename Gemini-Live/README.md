# Enhanced Gemini Computer Control Agent

An intelligent goal-oriented computer control agent that autonomously interacts with desktop applications through visual understanding and precise automation. Built upon the existing Gemini Live Computer Control architecture with sophisticated goal processing, multi-step task execution, and enhanced perception capabilities.

## 🎯 **Core Features**

### **Goal-Oriented Architecture**
- **Natural Language Goal Processing**: Parse complex objectives into actionable sub-tasks
- **Multi-Step Task Execution**: Break down goals with dependency management and error recovery
- **Success Criteria Validation**: Define and verify completion conditions
- **Progress Tracking**: Real-time goal progress monitoring and reporting

### **Enhanced AI Integration**
- **Gemini 1.5 Flash**: Real-time reasoning and decision-making (currently supported)
- **Future: Gemini 2.0 Flash Live**: True live streaming capabilities when available
- **Multimodal Processing**: Screenshots + natural language goals
- **Contextual Decision Making**: Learn from previous executions and user interactions

### **Advanced Perception System**
- **OCR Capabilities**: Extract text using EasyOCR or Tesseract
- **UI Element Detection**: Identify buttons, text fields, menus, and windows
- **Screen State Classification**: Understand application contexts and workflows
- **Change Detection**: Optimize capture frequency based on screen changes

### **Safety & Reliability**
- **Goal-Specific Safety Rules**: Prevent dangerous operations based on goal content
- **Execution Time Limits**: Automatic timeouts for goal execution
- **Rollback Capabilities**: Undo operations when tasks fail
- **User Confirmation**: Required approval for high-risk goals
- **Confidence Scoring**: AI decision confidence tracking

### **Performance Monitoring**
- **Goal Completion Metrics**: Success rates, execution times, command counts
- **System Resource Tracking**: CPU, memory usage during execution
- **Optimization Recommendations**: Automatic performance improvement suggestions
- **Benchmarking**: Performance testing with sample goals

### **Visual Feedback System**
- **Real-Time Progress**: Live goal progress visualization
- **Decision Process Display**: Show AI reasoning and decision-making
- **Execution Logs**: Detailed action history with timestamps
- **Interactive Dashboard**: Rich console interface with live updates

## 🚀 **Installation**

### Prerequisites
- Python 3.9 or higher
- Existing Gemini Computer Control system (optional for development)
- Google Gemini API key

### Setup

1. **Install dependencies:**
   ```bash
   cd Gemini-Live
   pip install -r requirements.txt
   ```

2. **Install OCR engines (choose one or both):**
   ```bash
   # For EasyOCR (recommended)
   pip install easyocr
   
   # For Tesseract
   # macOS: brew install tesseract
   # Ubuntu: sudo apt-get install tesseract-ocr
   # Windows: Download from GitHub releases
   pip install pytesseract
   ```

3. **Configure environment:**
   ```bash
   # Copy configuration from existing system or create new
   cp ../Gemini-Computer-Control/.env .env
   # Edit .env with your settings
   ```

## 📖 **Usage**

### **Goal Execution**
```bash
# Execute a simple goal
python -m main goal "Open calculator and compute 15 * 23"

# Step-by-step execution with confirmation
python -m main goal "Create a folder and organize files" --step-by-step

# Dry run (simulation only)
python -m main goal "Research ML papers and create summary" --dry-run
```

### **Goal Management**
```bash
# List all goals
python -m main goals list

# Check specific goal status
python -m main goals status <goal-id>

# Cancel an active goal
python -m main goals cancel <goal-id>

# View goal history
python -m main goals history
```

### **Autonomous Mode**
```bash
# Start autonomous goal-oriented mode
python -m main autonomous --goal "Organize my desktop files"
```

### **Performance & Testing**
```bash
# View performance summary
python -m main performance

# Test perception capabilities
python -m main test-perception

# Run benchmarks
python -m main benchmark
```

## 🎯 **Example Goals**

### **Simple Goals (1-3 steps)**
- "Open calculator and compute 15 * 23"
- "Take a screenshot and save it to desktop"
- "Open a text editor and type 'Hello World'"

### **Medium Complexity (4-10 steps)**
- "Open a text editor and write a Python function to calculate fibonacci numbers"
- "Create a new folder on desktop and move all image files into it"
- "Find and open a PDF file, then scroll to page 5"

### **Complex Goals (11+ steps)**
- "Research machine learning papers online and create a summary document"
- "Organize my desktop files by type into appropriate folders"
- "Create a presentation with 3 slides about renewable energy"

## 🏗️ **Architecture**

### **Core Components**

```
Gemini-Live/
├── core/                          # Core goal-oriented modules
│   ├── goal_processor.py         # Goal parsing and validation
│   ├── task_planner.py          # Multi-step task execution
│   ├── goal_state_manager.py    # Progress tracking and persistence
│   ├── enhanced_perception.py   # OCR and UI element detection
│   ├── performance_monitor.py   # Metrics and optimization
│   └── data_models.py           # Data structures and types
├── utils/                        # Utility modules
│   ├── visual_feedback.py       # Progress visualization
│   ├── goal_validators.py       # Goal validation utilities
│   └── ocr_utils.py             # OCR helper functions
├── enhanced_command_interpreter.py  # Main orchestrator
├── main.py                      # Enhanced CLI interface
└── examples/                    # Usage examples and tests
```

### **Integration with Existing System**
- **Extends**: Existing CommandInterpreter, GeminiClient, ComputerController
- **Maintains**: Full backward compatibility with current functionality
- **Enhances**: Safety systems, context tracking, and command execution
- **Preserves**: All existing safety features and emergency stops

## 🛡️ **Safety Features**

### **Enhanced Safety Rules**
- **Goal-Specific Restrictions**: Prevent file deletion, system changes based on goal content
- **Execution Time Limits**: Automatic timeouts (default: 5 minutes per goal)
- **Resource Monitoring**: Track CPU/memory usage and abort if excessive
- **User Confirmation**: Required for high-risk operations
- **Rollback System**: Automatic undo for failed operations

### **Risk Assessment**
- **Low Risk**: Simple UI interactions, text input
- **Medium Risk**: File operations, application installations
- **High Risk**: System modifications, network operations

## 📊 **Performance Monitoring**

### **Metrics Tracked**
- Goal completion success rates
- Average execution times by complexity
- Command success/failure rates
- System resource usage
- User interaction frequency

### **Optimization Features**
- Automatic performance recommendations
- Execution time optimization
- Resource usage alerts
- Command efficiency analysis

## 🎨 **Visual Feedback**

### **Live Display Features**
- Real-time goal progress bars
- Execution log with timestamps
- AI decision process visualization
- System status and controls
- Interactive dashboard

### **Progress Tracking**
- Goal completion percentage
- Sub-task status indicators
- Execution time estimates
- Success/failure indicators

## 🧪 **Testing & Development**

### **Run Examples**
```bash
# Run all example goals
python -m examples.goal_examples

# Test specific functionality
python -m main test-perception
python -m main benchmark
```

### **Development Mode**
The system includes mock implementations for development without the full Gemini Control system:
- MockComputerController
- MockGeminiClient  
- MockScreenRecorder

## 🔧 **Configuration**

### **Goal-Specific Settings**
```python
GOAL_MAX_EXECUTION_TIME = 300      # 5 minutes default
GOAL_MAX_SUBTASKS = 20             # Maximum sub-tasks per goal
GOAL_COMPLEXITY_THRESHOLD = 0.8    # Complexity scoring threshold
GOAL_AUTO_ROLLBACK = True          # Automatic rollback on failure
```

### **Perception Settings**
```python
OCR_ENGINE = "easyocr"             # or "tesseract"
OCR_LANGUAGES = ["en"]             # Supported languages
UI_DETECTION_CONFIDENCE = 0.7      # UI element detection threshold
SCREEN_CHANGE_THRESHOLD = 0.1      # Screen change sensitivity
```

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 **License**

MIT License - see LICENSE file for details.

## ⚠️ **Disclaimer**

This software provides AI control over computer systems. Users assume all risks associated with automated computer control. The authors are not responsible for any damage or unintended consequences resulting from the use of this software.

## 🔗 **Related Projects**

- [Gemini Computer Control](../Gemini-Computer-Control/) - Base system
- [Google Gemini API](https://ai.google.dev/gemini-api/docs/live) - AI model integration
- [PyAutoGUI](https://pyautogui.readthedocs.io/) - Computer automation
- [EasyOCR](https://github.com/JaidedAI/EasyOCR) - Text recognition
