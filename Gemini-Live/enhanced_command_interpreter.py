"""Enhanced command interpreter with goal-oriented capabilities."""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from loguru import logger

# Add the existing Gemini-Computer-Control to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Gemini-Computer-Control', 'src'))

try:
    from gemini_control.command_interpreter import CommandInterpreter
    from gemini_control.computer_controller import ComputerController
    from gemini_control.gemini_client import GeminiClient
    from gemini_control.screen_recorder import ScreenRecorder
    GEMINI_CONTROL_AVAILABLE = True
except ImportError:
    logger.warning("Gemini Control modules not available, using mock implementations")
    GEMINI_CONTROL_AVAILABLE = False

from .core import (
    GoalProcessor, TaskPlanner, GoalStateManager, 
    EnhancedPerception, PerformanceMonitor
)
from .core.data_models import Goal, SubTask, TaskStatus, ExecutionResult
from .utils.visual_feedback import VisualFeedback


class EnhancedCommandInterpreter:
    """Enhanced command interpreter with goal-oriented capabilities."""
    
    def __init__(self):
        """Initialize the enhanced command interpreter."""
        # Core components
        self.goal_processor = GoalProcessor()
        self.task_planner = TaskPlanner()
        self.goal_state_manager = GoalStateManager()
        self.enhanced_perception = EnhancedPerception()
        self.performance_monitor = PerformanceMonitor()
        self.visual_feedback = VisualFeedback()
        
        # Integration with existing system
        if GEMINI_CONTROL_AVAILABLE:
            self.base_interpreter = CommandInterpreter()
            self.computer_controller = self.base_interpreter.computer_controller
            self.gemini_client = self.base_interpreter.gemini_client
            self.screen_recorder = self.base_interpreter.screen_recorder
        else:
            # Mock implementations for development
            self.base_interpreter = None
            self.computer_controller = MockComputerController()
            self.gemini_client = MockGeminiClient()
            self.screen_recorder = MockScreenRecorder()
            
        # State tracking
        self.is_running = False
        self.current_goals: Dict[str, Goal] = {}
        
        logger.info("Enhanced Command Interpreter initialized")
        
    async def execute_goal(self, goal_text: str, step_by_step: bool = False, 
                          dry_run: bool = False) -> ExecutionResult:
        """
        Execute a goal-oriented instruction.
        
        Args:
            goal_text: Natural language description of the goal
            step_by_step: Whether to ask for confirmation between steps
            dry_run: Whether to simulate execution without actual commands
            
        Returns:
            ExecutionResult with execution details
        """
        logger.info(f"Executing goal: {goal_text}")
        
        try:
            # Start performance monitoring
            self.performance_monitor.start_monitoring()
            
            # Parse and validate the goal
            goal = self.goal_processor.parse_goal(goal_text)
            validation_result = self.goal_processor.validate_goal(goal)
            
            if not validation_result.is_valid:
                return ExecutionResult(
                    goal_id=goal.id,
                    success=False,
                    message=f"Goal validation failed: {', '.join(validation_result.issues)}"
                )
                
            # Add goal to state management
            self.goal_state_manager.add_goal(goal)
            self.current_goals[goal.id] = goal
            
            # Start visual feedback
            self.visual_feedback.start_goal_tracking(goal)
            
            # Show goal analysis
            self.visual_feedback.show_decision_process(
                "Goal Analysis",
                {
                    "complexity": goal.complexity.value,
                    "estimated_duration": goal.estimated_duration,
                    "sub_tasks": len(goal.sub_tasks),
                    "safety_level": goal.safety_level
                },
                f"Proceeding with {len(goal.sub_tasks)} sub-tasks"
            )
            
            if dry_run:
                return await self._simulate_goal_execution(goal)
            
            # Create execution plan
            execution_plan = self.task_planner.create_execution_plan(goal)
            
            # Show execution plan
            self.visual_feedback.add_execution_log(
                f"Created execution plan: {len(execution_plan.ordered_tasks)} tasks, "
                f"risk: {execution_plan.risk_assessment}"
            )
            
            # Request confirmation for high-risk goals
            if goal.user_confirmation_required or execution_plan.risk_assessment == "high":
                if not await self._request_goal_confirmation(goal, execution_plan):
                    self.goal_state_manager.cancel_goal(goal.id)
                    return ExecutionResult(
                        goal_id=goal.id,
                        success=False,
                        message="Goal cancelled by user"
                    )
                    
            # Execute the plan
            goal.status = TaskStatus.IN_PROGRESS
            goal.started_at = datetime.now()
            
            execution_result = await self.task_planner.execute_plan(
                execution_plan, goal, self.computer_controller, step_by_step
            )
            
            # Update goal status based on execution result
            if execution_result.success:
                goal.status = TaskStatus.COMPLETED
                goal.progress = 1.0
            else:
                goal.status = TaskStatus.FAILED
                
            goal.completed_at = datetime.now()
            
            # Record performance metrics
            metrics = self.performance_monitor.record_goal_execution(goal, execution_result)
            self.goal_state_manager.update_goal_metrics(goal.id, metrics)
            
            # Complete visual feedback
            self.visual_feedback.complete_goal_tracking(
                goal.id, execution_result.success, execution_result.message
            )
            
            # Check goal completion
            self.goal_state_manager.check_goal_completion(goal)
            
            # Clean up
            if goal.id in self.current_goals:
                del self.current_goals[goal.id]
                
            logger.info(f"Goal execution completed: {goal.id} ({'success' if execution_result.success else 'failed'})")
            return execution_result
            
        except Exception as e:
            logger.error(f"Error executing goal: {e}")
            
            # Clean up on error
            if goal.id in self.current_goals:
                goal = self.current_goals[goal.id]
                goal.status = TaskStatus.FAILED
                goal.completed_at = datetime.now()
                self.goal_state_manager.check_goal_completion(goal)
                del self.current_goals[goal.id]
                
            return ExecutionResult(
                goal_id=goal.id if 'goal' in locals() else "unknown",
                success=False,
                message=f"Goal execution error: {str(e)}",
                error_details=str(e)
            )
        finally:
            self.performance_monitor.stop_monitoring()
            
    async def start_autonomous_goal_mode(self, initial_goal: str = "") -> None:
        """
        Start autonomous mode with goal-oriented capabilities.
        
        Args:
            initial_goal: Initial goal to work towards
        """
        logger.info("Starting autonomous goal mode")
        self.is_running = True
        
        try:
            # Start visual feedback
            self.visual_feedback.start_live_display()
            
            # If we have an initial goal, execute it
            if initial_goal:
                await self.execute_goal(initial_goal)
                
            # Continue monitoring for new goals or goal updates
            while self.is_running:
                # Check for screen changes that might indicate new opportunities
                await self._monitor_for_goal_opportunities()
                
                # Update visual feedback
                self.visual_feedback.update_live_display()
                
                # Brief pause
                await asyncio.sleep(2.0)
                
        except KeyboardInterrupt:
            logger.info("Autonomous goal mode interrupted by user")
        except Exception as e:
            logger.error(f"Error in autonomous goal mode: {e}")
        finally:
            await self.stop()
            
    async def stop(self) -> None:
        """Stop the enhanced command interpreter."""
        self.is_running = False
        
        # Cancel any active goals
        for goal_id in list(self.current_goals.keys()):
            self.goal_state_manager.cancel_goal(goal_id)
            
        # Stop visual feedback
        self.visual_feedback.stop_live_display()
        
        # Stop base interpreter if available
        if self.base_interpreter:
            await self.base_interpreter.stop()
            
        logger.info("Enhanced command interpreter stopped")
        
    def get_goal_status(self, goal_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get status of goals.
        
        Args:
            goal_id: Specific goal ID, or None for all goals
            
        Returns:
            Dictionary with goal status information
        """
        if goal_id:
            goal = self.goal_state_manager.get_goal_by_id(goal_id)
            if goal:
                return {
                    "goal": goal.to_dict(),
                    "metrics": self.goal_state_manager.get_goal_metrics(goal_id),
                    "execution_history": self.goal_state_manager.get_execution_history(goal_id)
                }
            else:
                return {"error": f"Goal not found: {goal_id}"}
        else:
            return {
                "active_goals": [goal.to_dict() for goal in self.goal_state_manager.get_active_goals()],
                "completed_goals": [goal.to_dict() for goal in self.goal_state_manager.get_completed_goals()],
                "summary": self.goal_state_manager.get_goal_summary()
            }
            
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary and recommendations."""
        return {
            "summary": self.performance_monitor.get_performance_summary(),
            "recommendations": self.performance_monitor.get_optimization_recommendations()
        }
        
    async def _simulate_goal_execution(self, goal: Goal) -> ExecutionResult:
        """Simulate goal execution for dry run mode."""
        logger.info(f"Simulating goal execution: {goal.id}")
        
        # Simulate task execution
        for i, task in enumerate(goal.sub_tasks):
            progress = (i + 1) / len(goal.sub_tasks)
            self.visual_feedback.update_goal_progress(
                goal.id, progress, f"Simulating task: {task.description}"
            )
            
            # Simulate execution time
            await asyncio.sleep(0.5)
            
        return ExecutionResult(
            goal_id=goal.id,
            success=True,
            message="Goal simulation completed successfully",
            execution_time=len(goal.sub_tasks) * 0.5,
            commands_executed=sum(len(task.commands) for task in goal.sub_tasks)
        )
        
    async def _request_goal_confirmation(self, goal: Goal, execution_plan) -> bool:
        """Request user confirmation for goal execution."""
        # In a full implementation, this would show a detailed confirmation dialog
        logger.info(f"Goal confirmation requested for: {goal.description}")
        
        self.visual_feedback.add_execution_log(
            f"⚠️  Confirmation required for goal: {goal.description[:50]}..."
        )
        
        # For now, assume confirmation (in real implementation, would prompt user)
        return True
        
    async def _monitor_for_goal_opportunities(self) -> None:
        """Monitor screen for potential goal opportunities."""
        try:
            # Capture current screen
            if hasattr(self.screen_recorder, 'capture_screen'):
                image_data = self.screen_recorder.capture_screen()
                if image_data:
                    # Analyze screen state
                    screen_state = self.enhanced_perception.classify_screen_state(image_data)
                    
                    # Look for opportunities (placeholder logic)
                    if screen_state.active_application:
                        self.visual_feedback.add_decision_log(
                            f"Detected application: {screen_state.active_application}",
                            "Monitoring for goal opportunities"
                        )
                        
        except Exception as e:
            logger.debug(f"Error monitoring for goal opportunities: {e}")


# Mock implementations for development without full Gemini Control system
class MockComputerController:
    """Mock computer controller for development."""
    
    async def execute_command(self, command: Dict[str, Any]) -> bool:
        """Mock command execution."""
        await asyncio.sleep(0.1)  # Simulate execution time
        return True
        
    async def execute_commands(self, commands: List[Dict[str, Any]]) -> int:
        """Mock multiple command execution."""
        for command in commands:
            await self.execute_command(command)
        return len(commands)


class MockGeminiClient:
    """Mock Gemini client for development."""
    
    async def analyze_screen(self, image_data: bytes, instruction: str, context: str) -> Dict[str, Any]:
        """Mock screen analysis."""
        return {
            "commands": [{"action": "mock_action", "target": "mock_target"}],
            "message": "Mock analysis completed"
        }


class MockScreenRecorder:
    """Mock screen recorder for development."""
    
    def capture_screen(self) -> Optional[bytes]:
        """Mock screen capture."""
        return b"mock_image_data"
        
    def get_screen_info(self) -> Dict[str, Any]:
        """Mock screen info."""
        return {"width": 1920, "height": 1080}
