"""Enhanced CLI interface for goal-oriented computer control."""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table
from loguru import logger

from .enhanced_command_interpreter import Enhanced<PERSON>ommandInterpreter
from .core.data_models import TaskStatus

app = typer.Typer(help="Enhanced Gemini Computer Control - Goal-oriented AI automation")
console = Console()


def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    logger.remove()  # Remove default handler
    
    level = "DEBUG" if verbose else "INFO"
    
    # Add console handler
    logger.add(
        sys.stderr,
        level=level,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> - <level>{message}</level>"
    )
    
    # Add file handler
    logger.add(
        "enhanced_gemini_control.log",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days"
    )


@app.command()
def goal(
    goal_text: str = typer.Argument(..., help="Natural language description of the goal"),
    step_by_step: bool = typer.Option(False, "--step-by-step", "-s", help="Ask for confirmation between steps"),
    dry_run: bool = typer.Option(False, "--dry-run", "-d", help="Simulate execution without actual commands"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose logging")
):
    """Execute a goal-oriented instruction."""
    setup_logging(verbose)
    
    console.print(f"\n[bold blue]🎯 Goal:[/bold blue] {goal_text}")
    
    if dry_run:
        console.print("[yellow]🔍 Running in dry-run mode (simulation only)[/yellow]")
    
    if step_by_step:
        console.print("[cyan]👥 Step-by-step mode enabled (will ask for confirmation)[/cyan]")
    
    async def run_goal():
        interpreter = EnhancedCommandInterpreter()
        
        try:
            result = await interpreter.execute_goal(goal_text, step_by_step, dry_run)
            
            if result.success:
                console.print(f"\n[green]✅ Goal completed successfully![/green]")
                console.print(f"Execution time: {result.execution_time:.2f}s")
                console.print(f"Commands executed: {result.commands_executed}")
                if result.commands_failed > 0:
                    console.print(f"Commands failed: {result.commands_failed}")
            else:
                console.print(f"\n[red]❌ Goal failed![/red]")
                console.print(f"Error: {result.message}")
                if result.error_details:
                    console.print(f"Details: {result.error_details}")
                    
        except KeyboardInterrupt:
            console.print("\n[yellow]Goal execution cancelled by user[/yellow]")
        except Exception as e:
            console.print(f"\n[red]Error: {e}[/red]")
        finally:
            await interpreter.stop()
    
    asyncio.run(run_goal())


@app.command()
def autonomous(
    initial_goal: str = typer.Option("", "--goal", "-g", help="Initial goal to work towards"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose logging")
):
    """Start autonomous goal-oriented mode."""
    setup_logging(verbose)
    
    # Display warning
    console.print(Panel(
        "[bold red]⚠️  AUTONOMOUS GOAL MODE WARNING ⚠️[/bold red]\n\n"
        "This mode will continuously monitor your screen and execute goal-oriented tasks.\n"
        "The AI will have control over your mouse and keyboard to accomplish goals.\n\n"
        "[bold]Enhanced features:[/bold]\n"
        "• Goal-oriented task planning\n"
        "• Multi-step execution with error recovery\n"
        "• Enhanced perception with OCR and UI detection\n"
        "• Performance monitoring and optimization\n"
        "• Visual feedback and progress tracking\n\n"
        "[bold yellow]Press Ctrl+C to stop at any time[/bold yellow]",
        title="Enhanced Autonomous Mode",
        border_style="red"
    ))
    
    if not Confirm.ask("Do you want to continue?"):
        raise typer.Exit(0)
    
    if not initial_goal:
        initial_goal = Prompt.ask("Enter initial goal (or leave empty for general assistance)", default="")
    
    async def run_autonomous():
        interpreter = EnhancedCommandInterpreter()
        
        try:
            console.print(f"\n[green]🚀 Starting autonomous goal mode...[/green]")
            if initial_goal:
                console.print(f"Initial goal: {initial_goal}")
            
            await interpreter.start_autonomous_goal_mode(initial_goal)
            
        except KeyboardInterrupt:
            console.print("\n[yellow]Autonomous mode stopped by user[/yellow]")
        except Exception as e:
            console.print(f"\n[red]Error in autonomous mode: {e}[/red]")
        finally:
            await interpreter.stop()
    
    asyncio.run(run_autonomous())


@app.command("goals")
def goals_command(
    action: str = typer.Argument("list", help="Action: list, status, cancel, history"),
    goal_id: Optional[str] = typer.Argument(None, help="Goal ID for status/cancel actions")
):
    """Manage goals (list, status, cancel, history)."""
    setup_logging()
    
    async def run_goals_command():
        interpreter = EnhancedCommandInterpreter()
        
        try:
            if action == "list":
                status_data = interpreter.get_goal_status()
                
                # Show active goals
                if status_data["active_goals"]:
                    console.print("\n[bold blue]Active Goals:[/bold blue]")
                    interpreter.visual_feedback.show_goal_summary([
                        interpreter.goal_state_manager.get_goal_by_id(goal["id"])
                        for goal in status_data["active_goals"]
                    ])
                else:
                    console.print("\n[dim]No active goals[/dim]")
                
                # Show summary
                summary = status_data["summary"]
                console.print(f"\n[bold]Summary:[/bold]")
                console.print(f"• Total goals: {summary['total_goals']}")
                console.print(f"• Active: {summary['active_goals']}")
                console.print(f"• Completed: {summary['completed_goals']}")
                console.print(f"• Success rate: {summary['success_rate']:.1%}")
                
            elif action == "status":
                if not goal_id:
                    console.print("[red]Goal ID required for status command[/red]")
                    return
                
                status_data = interpreter.get_goal_status(goal_id)
                if "error" in status_data:
                    console.print(f"[red]{status_data['error']}[/red]")
                else:
                    goal_data = status_data["goal"]
                    console.print(f"\n[bold]Goal Status: {goal_id[:8]}[/bold]")
                    console.print(f"Description: {goal_data['description']}")
                    console.print(f"Status: {goal_data['status']}")
                    console.print(f"Progress: {goal_data['progress']:.1%}")
                    console.print(f"Sub-tasks: {len(goal_data['sub_tasks'])}")
                    
                    if status_data["metrics"]:
                        interpreter.visual_feedback.show_performance_metrics(status_data["metrics"])
                
            elif action == "cancel":
                if not goal_id:
                    console.print("[red]Goal ID required for cancel command[/red]")
                    return
                
                success = interpreter.goal_state_manager.cancel_goal(goal_id)
                if success:
                    console.print(f"[green]Goal {goal_id[:8]} cancelled successfully[/green]")
                else:
                    console.print(f"[red]Goal {goal_id[:8]} not found or already completed[/red]")
                
            elif action == "history":
                completed_goals = interpreter.goal_state_manager.get_completed_goals()
                if completed_goals:
                    console.print("\n[bold blue]Goal History:[/bold blue]")
                    interpreter.visual_feedback.show_goal_summary(completed_goals[-10:])  # Last 10
                else:
                    console.print("\n[dim]No completed goals[/dim]")
                
            else:
                console.print(f"[red]Unknown action: {action}[/red]")
                console.print("Available actions: list, status, cancel, history")
                
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
        finally:
            await interpreter.stop()
    
    asyncio.run(run_goals_command())


@app.command()
def performance():
    """Show performance summary and optimization recommendations."""
    setup_logging()
    
    async def run_performance():
        interpreter = EnhancedCommandInterpreter()
        
        try:
            perf_data = interpreter.get_performance_summary()
            
            # Show performance summary
            summary = perf_data["summary"]
            console.print("\n[bold blue]Performance Summary (Last 24 hours):[/bold blue]")
            
            if "error" not in summary:
                sys_perf = summary["system_performance"]
                goal_perf = summary["goal_execution"]
                
                table = Table(title="System Performance")
                table.add_column("Metric", style="cyan")
                table.add_column("Average", style="white")
                table.add_column("Peak", style="yellow")
                
                table.add_row("CPU Usage", f"{sys_perf['avg_cpu_percent']:.1f}%", f"{sys_perf['max_cpu_percent']:.1f}%")
                table.add_row("Memory Usage", f"{sys_perf['avg_memory_percent']:.1f}%", f"{sys_perf['max_memory_percent']:.1f}%")
                table.add_row("Commands/sec", f"{sys_perf['avg_commands_per_second']:.2f}", "-")
                
                console.print(table)
                
                console.print(f"\n[bold]Goal Execution:[/bold]")
                console.print(f"• Total goals: {goal_perf['total_goals']}")
                console.print(f"• Success rate: {goal_perf['success_rate']:.1%}")
                console.print(f"• Average execution time: {goal_perf['avg_execution_time']:.2f}s")
            
            # Show recommendations
            recommendations = perf_data["recommendations"]
            if recommendations:
                console.print("\n[bold yellow]Optimization Recommendations:[/bold yellow]")
                for i, rec in enumerate(recommendations, 1):
                    console.print(f"{i}. {rec}")
            
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
        finally:
            await interpreter.stop()
    
    asyncio.run(run_performance())


@app.command()
def test_perception():
    """Test enhanced perception capabilities (OCR and UI detection)."""
    setup_logging()
    
    async def run_test():
        interpreter = EnhancedCommandInterpreter()
        
        try:
            console.print("[blue]Testing enhanced perception capabilities...[/blue]")
            
            # Capture screen
            image_data = interpreter.screen_recorder.capture_screen()
            if not image_data:
                console.print("[red]Failed to capture screen[/red]")
                return
            
            console.print("[green]✅ Screen capture successful[/green]")
            
            # Test OCR
            console.print("\n[yellow]Testing OCR...[/yellow]")
            text_elements = interpreter.enhanced_perception.extract_text_from_screen(image_data)
            console.print(f"Found {len(text_elements)} text elements")
            
            if text_elements:
                for i, elem in enumerate(text_elements[:5]):  # Show first 5
                    console.print(f"  {i+1}. '{elem.text}' (confidence: {elem.confidence:.2f})")
            
            # Test UI detection
            console.print("\n[yellow]Testing UI element detection...[/yellow]")
            ui_elements = interpreter.enhanced_perception.detect_ui_elements(image_data)
            console.print(f"Found {len(ui_elements)} UI elements")
            
            if ui_elements:
                for i, elem in enumerate(ui_elements[:5]):  # Show first 5
                    console.print(f"  {i+1}. {elem.element_type} (confidence: {elem.confidence:.2f})")
            
            # Test screen state classification
            console.print("\n[yellow]Testing screen state classification...[/yellow]")
            screen_state = interpreter.enhanced_perception.classify_screen_state(image_data)
            console.print(f"Active application: {screen_state.active_application or 'Unknown'}")
            console.print(f"Total elements detected: {len(screen_state.ui_elements) + len(screen_state.text_elements)}")
            
            console.print("\n[green]🎉 Perception test completed![/green]")
            
        except Exception as e:
            console.print(f"[red]Error during perception test: {e}[/red]")
        finally:
            await interpreter.stop()
    
    asyncio.run(run_test())


@app.command()
def benchmark():
    """Run performance benchmarks with sample goals."""
    setup_logging()
    
    sample_goals = [
        "Open calculator and compute 15 * 23",
        "Take a screenshot and save it to desktop", 
        "Open a text editor and type 'Hello World'",
        "Create a new folder on desktop called 'test'",
        "Open system preferences and check the current time"
    ]
    
    async def run_benchmark():
        interpreter = EnhancedCommandInterpreter()
        
        try:
            console.print("[blue]Running performance benchmarks...[/blue]")
            console.print(f"Testing {len(sample_goals)} sample goals in dry-run mode\n")
            
            results = []
            
            for i, goal_text in enumerate(sample_goals, 1):
                console.print(f"[cyan]Benchmark {i}/{len(sample_goals)}:[/cyan] {goal_text}")
                
                result = await interpreter.execute_goal(goal_text, dry_run=True)
                results.append(result)
                
                status = "✅" if result.success else "❌"
                console.print(f"{status} Completed in {result.execution_time:.2f}s")
                console.print()
            
            # Show summary
            successful = sum(1 for r in results if r.success)
            total_time = sum(r.execution_time for r in results)
            avg_time = total_time / len(results)
            
            console.print("[bold green]Benchmark Results:[/bold green]")
            console.print(f"• Goals tested: {len(results)}")
            console.print(f"• Successful: {successful}/{len(results)} ({successful/len(results):.1%})")
            console.print(f"• Total time: {total_time:.2f}s")
            console.print(f"• Average time per goal: {avg_time:.2f}s")
            
        except Exception as e:
            console.print(f"[red]Error during benchmark: {e}[/red]")
        finally:
            await interpreter.stop()
    
    asyncio.run(run_benchmark())


if __name__ == "__main__":
    app()
