"""
Main entry point for the Gemini Live Computer Control Agent.
"""

import asyncio
import sys
import signal
from typing import Optional
from rich.console import <PERSON>sole
from rich.panel import Panel
from rich.text import Text

from config.settings import settings
from utils.logger import logger
from utils.safety import safety_manager
from core.gemini_client import gemini_client
from core.screen_analyzer import screen_analyzer
from core.action_executor import action_executor
from core.command_processor import command_processor


class GeminiControlAgent:
    """Main computer control agent class."""
    
    def __init__(self):
        self.console = Console()
        self.running = False
        self.interactive_mode = True
        
    async def initialize(self) -> bool:
        """Initialize the agent and all its components."""
        try:
            self.console.print(Panel.fit(
                "[bold blue]Gemini Live Computer Control Agent[/bold blue]\n"
                "Initializing components...",
                border_style="blue"
            ))
            
            # Check API key
            if not settings.gemini.api_key:
                logger.error("Gemini API key not found. Please set GEMINI_API_KEY environment variable.")
                return False
            
            # Connect to Gemini API
            logger.info("Connecting to Gemini API...")
            if not await gemini_client.connect():
                logger.error("Failed to connect to Gemini API")
                return False
            
            # Test screen capture
            logger.info("Testing screen capture...")
            try:
                test_screenshot = screen_analyzer.capture_screenshot()
                logger.info(f"Screen capture successful: {test_screenshot.width}x{test_screenshot.height}")
            except Exception as e:
                logger.error(f"Screen capture failed: {e}")
                return False
            
            # Display safety settings
            if settings.safety.enable_safety_checks:
                logger.info("Safety checks enabled")
                if settings.safety.restricted_areas:
                    logger.info(f"Restricted areas: {settings.safety.restricted_areas}")
                logger.info(f"Max actions per minute: {settings.safety.max_actions_per_minute}")
            else:
                logger.warning("Safety checks disabled - use with caution!")
            
            self.console.print("[green]✓ Agent initialized successfully[/green]")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize agent: {e}")
            return False
    
    async def run_interactive(self):
        """Run the agent in interactive mode."""
        self.console.print(Panel.fit(
            "[bold green]Agent Ready[/bold green]\n"
            "Enter commands or type 'help' for assistance.\n"
            "Type 'quit' or 'exit' to stop the agent.",
            border_style="green"
        ))
        
        self.running = True
        
        while self.running:
            try:
                # Get user input
                command = await self._get_user_input()
                
                if not command:
                    continue
                
                # Handle special commands
                if command.lower() in ['quit', 'exit', 'stop']:
                    break
                elif command.lower() == 'help':
                    self._show_help()
                    continue
                elif command.lower() == 'status':
                    self._show_status()
                    continue
                elif command.lower() == 'clear':
                    gemini_client.clear_conversation_history()
                    self.console.print("[yellow]Conversation history cleared[/yellow]")
                    continue
                
                # Process the command
                await self._process_command(command)
                
            except KeyboardInterrupt:
                self.console.print("\n[yellow]Interrupted by user[/yellow]")
                break
            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                self.console.print(f"[red]Error: {e}[/red]")
    
    async def _get_user_input(self) -> str:
        """Get user input asynchronously."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, input, "Command: ")
    
    async def _process_command(self, command: str):
        """Process a user command."""
        try:
            # Process the command
            processed_cmd = command_processor.process_user_command(command)
            logger.info(f"Command type: {processed_cmd.command_type.value}, Intent: {processed_cmd.intent}")
            
            # If the command requires screen analysis, capture screenshot and send to Gemini
            if processed_cmd.requires_screen_analysis:
                self.console.print("[yellow]Analyzing screen...[/yellow]")
                
                # Capture screenshot
                screenshot = screen_analyzer.capture_screenshot()
                image_base64 = screen_analyzer.screenshot_to_base64(screenshot)
                screen_info = screen_analyzer.get_screen_info()
                
                # Send to Gemini for analysis
                response = await gemini_client.send_screen_analysis_request(
                    image_base64, command, screen_info
                )
                
                if response:
                    # Validate the response
                    is_valid, error_msg = command_processor.validate_ai_response(response)
                    
                    if is_valid:
                        # Extract actions
                        actions = command_processor.extract_action_sequence(response)
                        
                        # Show explanation if available
                        if 'explanation' in response:
                            self.console.print(f"[blue]AI: {response['explanation']}[/blue]")
                        
                        # Execute actions
                        await self._execute_actions(actions)
                    else:
                        logger.error(f"Invalid AI response: {error_msg}")
                        self.console.print(f"[red]Invalid response from AI: {error_msg}[/red]")
                else:
                    self.console.print("[red]Failed to get response from AI[/red]")
            
            else:
                # Direct action - execute immediately
                if processed_cmd.command_type.value == 'direct_action':
                    action = {
                        'type': processed_cmd.parameters.get('action_type'),
                        **{k: v for k, v in processed_cmd.parameters.items() if k != 'action_type'}
                    }
                    await self._execute_actions([action])
                else:
                    self.console.print("[yellow]Command processed but no actions to execute[/yellow]")
                    
        except Exception as e:
            logger.error(f"Error processing command: {e}")
            self.console.print(f"[red]Error processing command: {e}[/red]")
    
    async def _execute_actions(self, actions: list):
        """Execute a list of actions."""
        if not actions:
            self.console.print("[yellow]No actions to execute[/yellow]")
            return
        
        self.console.print(f"[cyan]Executing {len(actions)} action(s)...[/cyan]")
        
        for i, action in enumerate(actions, 1):
            self.console.print(f"[dim]Action {i}: {action.get('type', 'unknown')}[/dim]")
            
            success = action_executor.execute_action(action)
            
            if success:
                self.console.print(f"[green]✓ Action {i} completed[/green]")
            else:
                self.console.print(f"[red]✗ Action {i} failed[/red]")
                break  # Stop on first failure
        
        # Show execution statistics
        stats = action_executor.get_statistics()
        self.console.print(f"[dim]Total executed: {stats['actions_executed']}, "
                          f"Failed: {stats['actions_failed']}, "
                          f"Success rate: {stats['success_rate']:.1f}%[/dim]")
    
    def _show_help(self):
        """Show help information."""
        help_text = """
[bold]Available Commands:[/bold]

[cyan]Direct Actions:[/cyan]
• click 100,200 - Click at coordinates (100, 200)
• double click 150,300 - Double-click at coordinates
• right click 200,400 - Right-click at coordinates
• move 250,500 - Move mouse to coordinates
• type "Hello World" - Type text
• press enter - Press a key
• press ctrl+c - Press key combination

[cyan]Screen Analysis Commands:[/cyan]
• click on the button - Find and click a button
• open calculator - Open an application
• search for "python" - Search for something
• find the save button - Locate an element

[cyan]System Commands:[/cyan]
• help - Show this help
• status - Show agent status
• clear - Clear conversation history
• quit/exit - Stop the agent

[yellow]Note: Commands that don't specify exact coordinates will use AI vision to analyze the screen.[/yellow]
"""
        self.console.print(Panel(help_text, title="Help", border_style="cyan"))
    
    def _show_status(self):
        """Show agent status."""
        stats = action_executor.get_statistics()
        
        status_text = f"""
[bold]Agent Status:[/bold]

[cyan]Connection:[/cyan] {'✓ Connected' if gemini_client.is_connected else '✗ Disconnected'}
[cyan]Safety Checks:[/cyan] {'✓ Enabled' if settings.safety.enable_safety_checks else '✗ Disabled'}
[cyan]Actions Executed:[/cyan] {stats['actions_executed']}
[cyan]Actions Failed:[/cyan] {stats['actions_failed']}
[cyan]Success Rate:[/cyan] {stats['success_rate']:.1f}%
[cyan]Conversation History:[/cyan] {len(gemini_client.get_conversation_history())} messages
"""
        self.console.print(Panel(status_text, title="Status", border_style="blue"))
    
    async def shutdown(self):
        """Shutdown the agent gracefully."""
        self.console.print("[yellow]Shutting down agent...[/yellow]")
        self.running = False
        
        try:
            await gemini_client.disconnect()
            logger.info("Agent shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


async def main():
    """Main entry point."""
    agent = GeminiControlAgent()
    
    # Set up signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        asyncio.create_task(agent.shutdown())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Initialize the agent
        if not await agent.initialize():
            sys.exit(1)
        
        # Run the agent
        await agent.run_interactive()
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
    finally:
        await agent.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
