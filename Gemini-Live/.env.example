# Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Safety Settings
ENABLE_SAFETY_CHECKS=true
RESTRICTED_AREAS=0,0,100,50  # x1,y1,x2,y2 format for restricted screen areas
MAX_ACTIONS_PER_MINUTE=60
REQUIRE_CONFIRMATION=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/agent.log
LOG_ACTIONS=true

# Performance Settings
SCREENSHOT_QUALITY=80
SCREENSHOT_INTERVAL=1.0
MAX_IMAGE_SIZE=1920x1080
