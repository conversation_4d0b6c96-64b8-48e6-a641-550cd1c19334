"""
Configuration management for the Gemini Live Computer Control Agent.
"""

import os
from typing import List, <PERSON><PERSON>, Optional
from pydantic import BaseModel, <PERSON>
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class SafetySettings(BaseModel):
    """Safety configuration settings."""
    enable_safety_checks: bool = Field(default=True)
    restricted_areas: List[Tuple[int, int, int, int]] = Field(default_factory=list)
    max_actions_per_minute: int = Field(default=60)
    require_confirmation: bool = Field(default=False)
    forbidden_actions: List[str] = Field(default_factory=lambda: [
        "delete_system_files",
        "modify_system_settings",
        "access_sensitive_data"
    ])


class PerformanceSettings(BaseModel):
    """Performance configuration settings."""
    screenshot_quality: int = Field(default=80, ge=1, le=100)
    screenshot_interval: float = Field(default=1.0, ge=0.1)
    max_image_size: Tuple[int, int] = Field(default=(1920, 1080))
    compression_enabled: bool = Field(default=True)


class LoggingSettings(BaseModel):
    """Logging configuration settings."""
    log_level: str = Field(default="INFO")
    log_file: Optional[str] = Field(default="logs/agent.log")
    log_actions: bool = Field(default=True)
    log_api_calls: bool = Field(default=True)


class GeminiSettings(BaseModel):
    """Gemini API configuration settings."""
    api_key: str = Field(...)
    model_name: str = Field(default="gemini-2.0-flash-live-001")
    max_tokens: int = Field(default=8192)
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)


class Settings(BaseModel):
    """Main configuration class."""
    gemini: GeminiSettings
    safety: SafetySettings = Field(default_factory=SafetySettings)
    performance: PerformanceSettings = Field(default_factory=PerformanceSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)

    @classmethod
    def from_env(cls) -> "Settings":
        """Create settings from environment variables."""
        # Parse restricted areas from string format
        restricted_areas = []
        areas_str = os.getenv("RESTRICTED_AREAS", "")
        if areas_str:
            try:
                coords = [int(x.strip()) for x in areas_str.split(",")]
                if len(coords) == 4:
                    restricted_areas = [(coords[0], coords[1], coords[2], coords[3])]
            except ValueError:
                pass

        # Parse image size
        size_str = os.getenv("MAX_IMAGE_SIZE", "1920x1080")
        try:
            width, height = map(int, size_str.split("x"))
            max_image_size = (width, height)
        except ValueError:
            max_image_size = (1920, 1080)

        return cls(
            gemini=GeminiSettings(
                api_key=os.getenv("GEMINI_API_KEY", ""),
            ),
            safety=SafetySettings(
                enable_safety_checks=os.getenv("ENABLE_SAFETY_CHECKS", "true").lower() == "true",
                restricted_areas=restricted_areas,
                max_actions_per_minute=int(os.getenv("MAX_ACTIONS_PER_MINUTE", "60")),
                require_confirmation=os.getenv("REQUIRE_CONFIRMATION", "false").lower() == "true",
            ),
            performance=PerformanceSettings(
                screenshot_quality=int(os.getenv("SCREENSHOT_QUALITY", "80")),
                screenshot_interval=float(os.getenv("SCREENSHOT_INTERVAL", "1.0")),
                max_image_size=max_image_size,
            ),
            logging=LoggingSettings(
                log_level=os.getenv("LOG_LEVEL", "INFO"),
                log_file=os.getenv("LOG_FILE", "logs/agent.log"),
                log_actions=os.getenv("LOG_ACTIONS", "true").lower() == "true",
            ),
        )


# Global settings instance
settings = Settings.from_env()
