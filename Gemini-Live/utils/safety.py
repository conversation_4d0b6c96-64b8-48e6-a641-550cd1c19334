"""
Safety mechanisms for the Gemini Live Computer Control Agent.
"""

import time
from typing import List, Tu<PERSON>, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

from config.settings import settings
from utils.logger import logger


@dataclass
class ActionRecord:
    """Record of an action taken by the agent."""
    action_type: str
    timestamp: datetime
    coordinates: Optional[Tuple[int, int]] = None
    details: Optional[Dict[str, Any]] = None


class SafetyManager:
    """Manages safety checks and restrictions for the computer control agent."""
    
    def __init__(self):
        self.action_history: List[ActionRecord] = []
        self.last_action_time = time.time()
        self.actions_this_minute = 0
        self.minute_start = time.time()
    
    def is_action_safe(self, action_type: str, coordinates: Optional[Tuple[int, int]] = None, 
                      details: Optional[Dict[str, Any]] = None) -> Tuple[bool, str]:
        """
        Check if an action is safe to execute.
        
        Args:
            action_type: Type of action (e.g., 'click', 'type', 'move')
            coordinates: Screen coordinates for the action
            details: Additional action details
            
        Returns:
            Tuple of (is_safe, reason)
        """
        if not settings.safety.enable_safety_checks:
            return True, "Safety checks disabled"
        
        # Check rate limiting
        if not self._check_rate_limit():
            return False, "Rate limit exceeded"
        
        # Check restricted areas
        if coordinates and not self._check_restricted_areas(coordinates):
            return False, "Action in restricted area"
        
        # Check forbidden actions
        if not self._check_forbidden_actions(action_type, details):
            return False, "Forbidden action type"
        
        # Check for suspicious patterns
        if not self._check_action_patterns(action_type, coordinates):
            return False, "Suspicious action pattern detected"
        
        return True, "Action approved"
    
    def record_action(self, action_type: str, coordinates: Optional[Tuple[int, int]] = None,
                     details: Optional[Dict[str, Any]] = None):
        """Record an action for safety monitoring."""
        record = ActionRecord(
            action_type=action_type,
            timestamp=datetime.now(),
            coordinates=coordinates,
            details=details
        )
        self.action_history.append(record)
        
        # Keep only recent history (last hour)
        cutoff_time = datetime.now() - timedelta(hours=1)
        self.action_history = [r for r in self.action_history if r.timestamp > cutoff_time]
        
        logger.log_action(action_type, {"coordinates": coordinates, "details": details})
    
    def _check_rate_limit(self) -> bool:
        """Check if the action rate limit is exceeded."""
        current_time = time.time()
        
        # Reset counter if a new minute has started
        if current_time - self.minute_start >= 60:
            self.actions_this_minute = 0
            self.minute_start = current_time
        
        # Check if we're within the rate limit
        if self.actions_this_minute >= settings.safety.max_actions_per_minute:
            logger.log_safety_check("rate_limit", False, 
                                   f"Exceeded {settings.safety.max_actions_per_minute} actions per minute")
            return False
        
        self.actions_this_minute += 1
        return True
    
    def _check_restricted_areas(self, coordinates: Tuple[int, int]) -> bool:
        """Check if coordinates are in a restricted area."""
        x, y = coordinates
        
        for x1, y1, x2, y2 in settings.safety.restricted_areas:
            if x1 <= x <= x2 and y1 <= y <= y2:
                logger.log_safety_check("restricted_area", False, 
                                       f"Coordinates ({x}, {y}) in restricted area ({x1}, {y1}, {x2}, {y2})")
                return False
        
        return True
    
    def _check_forbidden_actions(self, action_type: str, details: Optional[Dict[str, Any]]) -> bool:
        """Check if the action type is forbidden."""
        if action_type in settings.safety.forbidden_actions:
            logger.log_safety_check("forbidden_action", False, f"Action type '{action_type}' is forbidden")
            return False
        
        # Check for dangerous keyboard combinations
        if action_type == "key_combination" and details:
            dangerous_combos = [
                ["ctrl", "alt", "del"],
                ["cmd", "option", "esc"],
                ["alt", "f4"],
            ]
            
            keys = details.get("keys", [])
            if any(all(key in keys for key in combo) for combo in dangerous_combos):
                logger.log_safety_check("dangerous_key_combo", False, f"Dangerous key combination: {keys}")
                return False
        
        return True
    
    def _check_action_patterns(self, action_type: str, coordinates: Optional[Tuple[int, int]]) -> bool:
        """Check for suspicious action patterns."""
        # Check for rapid repeated actions at the same location
        if coordinates and len(self.action_history) >= 5:
            recent_actions = self.action_history[-5:]
            same_location_count = sum(
                1 for record in recent_actions 
                if record.coordinates == coordinates and record.action_type == action_type
            )
            
            if same_location_count >= 4:
                logger.log_safety_check("repeated_action", False, 
                                       f"Too many repeated {action_type} actions at {coordinates}")
                return False
        
        # Check for excessive mouse movement
        if action_type == "move" and len(self.action_history) >= 10:
            recent_moves = [r for r in self.action_history[-10:] if r.action_type == "move"]
            if len(recent_moves) >= 8:
                logger.log_safety_check("excessive_movement", False, "Too many mouse movements in short time")
                return False
        
        return True
    
    def request_confirmation(self, action_description: str) -> bool:
        """Request user confirmation for an action."""
        if not settings.safety.require_confirmation:
            return True
        
        try:
            response = input(f"Confirm action: {action_description} (y/N): ").strip().lower()
            confirmed = response in ['y', 'yes']
            logger.log_safety_check("user_confirmation", confirmed, f"Action: {action_description}")
            return confirmed
        except (EOFError, KeyboardInterrupt):
            logger.log_safety_check("user_confirmation", False, "User interrupted confirmation")
            return False
    
    def emergency_stop(self):
        """Emergency stop mechanism."""
        logger.critical("EMERGENCY STOP ACTIVATED - All actions halted")
        # Could implement additional emergency measures here
        raise SystemExit("Emergency stop activated")


# Global safety manager instance
safety_manager = SafetyManager()
