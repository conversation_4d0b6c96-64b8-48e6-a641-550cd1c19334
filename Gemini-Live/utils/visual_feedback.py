"""Visual feedback system for goal execution progress."""

import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, TaskID, BarColumn, TextColumn, TimeRemainingColumn
from rich.table import Table
from rich.live import Live
from rich.layout import Layout
from rich.text import Text
from loguru import logger

from ..core.data_models import Goal, SubTask, TaskStatus, GoalMetrics


class VisualFeedback:
    """Provides visual feedback for goal execution progress."""
    
    def __init__(self, update_interval: float = 2.0):
        """
        Initialize the visual feedback system.
        
        Args:
            update_interval: Update interval for live display (seconds)
        """
        self.console = Console()
        self.update_interval = update_interval
        self.active_goals: Dict[str, Goal] = {}
        self.goal_progress: Dict[str, TaskID] = {}
        self.execution_log: List[str] = []
        self.decision_log: List[str] = []
        
        # Rich components
        self.progress = Progress(
            TextColumn("[bold blue]{task.fields[goal_name]}", justify="left"),
            BarColumn(bar_width=None),
            "[progress.percentage]{task.percentage:>3.1f}%",
            "•",
            TimeRemainingColumn(),
            console=self.console
        )
        
        self.live_display = None
        self.is_displaying = False
        
    def start_goal_tracking(self, goal: Goal) -> None:
        """
        Start tracking a goal's progress.
        
        Args:
            goal: Goal to track
        """
        self.active_goals[goal.id] = goal
        
        # Add progress bar for the goal
        task_id = self.progress.add_task(
            f"goal_{goal.id}",
            goal_name=goal.description[:50] + "..." if len(goal.description) > 50 else goal.description,
            total=100
        )
        self.goal_progress[goal.id] = task_id
        
        logger.info(f"Started tracking goal: {goal.id}")
        
    def update_goal_progress(self, goal_id: str, progress: float, status_message: str = "") -> None:
        """
        Update progress for a goal.
        
        Args:
            goal_id: ID of the goal
            progress: Progress value (0.0 to 1.0)
            status_message: Optional status message
        """
        if goal_id in self.goal_progress:
            task_id = self.goal_progress[goal_id]
            self.progress.update(task_id, completed=progress * 100)
            
            if status_message:
                self.add_execution_log(f"[{goal_id[:8]}] {status_message}")
                
        logger.debug(f"Updated goal progress: {goal_id} -> {progress:.1%}")
        
    def complete_goal_tracking(self, goal_id: str, success: bool, message: str = "") -> None:
        """
        Complete tracking for a goal.
        
        Args:
            goal_id: ID of the goal
            success: Whether the goal completed successfully
            message: Completion message
        """
        if goal_id in self.goal_progress:
            task_id = self.goal_progress[goal_id]
            self.progress.update(task_id, completed=100)
            
            status = "✅ COMPLETED" if success else "❌ FAILED"
            self.add_execution_log(f"[{goal_id[:8]}] {status}: {message}")
            
            # Remove from active tracking after a delay
            # In a real implementation, you might want to keep completed goals visible
            
        if goal_id in self.active_goals:
            del self.active_goals[goal_id]
            
        logger.info(f"Completed goal tracking: {goal_id} ({'success' if success else 'failed'})")
        
    def add_execution_log(self, message: str) -> None:
        """
        Add a message to the execution log.
        
        Args:
            message: Log message
        """
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.execution_log.append(log_entry)
        
        # Limit log size
        if len(self.execution_log) > 50:
            self.execution_log = self.execution_log[-25:]
            
    def add_decision_log(self, decision: str, reasoning: str = "") -> None:
        """
        Add an AI decision to the decision log.
        
        Args:
            decision: Decision made by the AI
            reasoning: Reasoning behind the decision
        """
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] 🤖 {decision}"
        if reasoning:
            log_entry += f" (Reason: {reasoning})"
            
        self.decision_log.append(log_entry)
        
        # Limit log size
        if len(self.decision_log) > 30:
            self.decision_log = self.decision_log[-15:]
            
    def start_live_display(self) -> None:
        """Start the live display."""
        if self.is_displaying:
            return
            
        layout = self._create_layout()
        self.live_display = Live(layout, console=self.console, refresh_per_second=1/self.update_interval)
        self.live_display.start()
        self.is_displaying = True
        
        logger.info("Started live visual feedback display")
        
    def stop_live_display(self) -> None:
        """Stop the live display."""
        if self.live_display and self.is_displaying:
            self.live_display.stop()
            self.is_displaying = False
            logger.info("Stopped live visual feedback display")
            
    def update_live_display(self) -> None:
        """Update the live display with current information."""
        if not self.is_displaying or not self.live_display:
            return
            
        layout = self._create_layout()
        self.live_display.update(layout)
        
    def show_goal_summary(self, goals: List[Goal]) -> None:
        """
        Show a summary of goals.
        
        Args:
            goals: List of goals to summarize
        """
        table = Table(title="Goal Summary")
        table.add_column("ID", style="cyan", no_wrap=True)
        table.add_column("Description", style="white")
        table.add_column("Status", style="bold")
        table.add_column("Progress", style="green")
        table.add_column("Tasks", style="blue")
        
        for goal in goals:
            status_color = {
                TaskStatus.PENDING: "yellow",
                TaskStatus.IN_PROGRESS: "blue", 
                TaskStatus.COMPLETED: "green",
                TaskStatus.FAILED: "red",
                TaskStatus.CANCELLED: "dim"
            }.get(goal.status, "white")
            
            completed_tasks = sum(1 for task in goal.sub_tasks if task.status == TaskStatus.COMPLETED)
            total_tasks = len(goal.sub_tasks)
            
            table.add_row(
                goal.id[:8],
                goal.description[:40] + "..." if len(goal.description) > 40 else goal.description,
                f"[{status_color}]{goal.status.value.upper()}[/{status_color}]",
                f"{goal.progress:.1%}",
                f"{completed_tasks}/{total_tasks}"
            )
            
        self.console.print(table)
        
    def show_performance_metrics(self, metrics: GoalMetrics) -> None:
        """
        Show performance metrics for a goal.
        
        Args:
            metrics: Goal metrics to display
        """
        panel_content = f"""
[bold]Goal ID:[/bold] {metrics.goal_id[:8]}
[bold]Execution Time:[/bold] {metrics.execution_time:.2f}s
[bold]Success Rate:[/bold] {metrics.success_rate:.1%}
[bold]Commands:[/bold] {metrics.commands_executed} executed, {metrics.commands_failed} failed
[bold]Retries:[/bold] {metrics.retries_performed}
[bold]User Interactions:[/bold] {metrics.user_interactions}
[bold]System Usage:[/bold] CPU {metrics.cpu_usage_percent:.1f}%, Memory {metrics.memory_usage_mb:.1f}MB
        """.strip()
        
        panel = Panel(
            panel_content,
            title="Performance Metrics",
            border_style="blue"
        )
        
        self.console.print(panel)
        
    def show_decision_process(self, step: str, analysis: Dict[str, Any], decision: str) -> None:
        """
        Show the AI's decision-making process.
        
        Args:
            step: Current step in the process
            analysis: Analysis data
            decision: Decision made
        """
        self.add_decision_log(f"Step: {step}", f"Decision: {decision}")
        
        if self.is_displaying:
            self.update_live_display()
        else:
            # Show immediate feedback
            panel_content = f"""
[bold]Step:[/bold] {step}
[bold]Analysis:[/bold] {json.dumps(analysis, indent=2) if analysis else 'None'}
[bold]Decision:[/bold] {decision}
            """.strip()
            
            panel = Panel(
                panel_content,
                title="🤖 AI Decision Process",
                border_style="cyan"
            )
            
            self.console.print(panel)
            
    def _create_layout(self) -> Layout:
        """Create the layout for live display."""
        layout = Layout()
        
        # Split into main sections
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=8)
        )
        
        # Split body into progress and logs
        layout["body"].split_row(
            Layout(name="progress"),
            Layout(name="logs")
        )
        
        # Split logs into execution and decisions
        layout["logs"].split_column(
            Layout(name="execution_log"),
            Layout(name="decision_log")
        )
        
        # Populate sections
        layout["header"].update(self._create_header())
        layout["progress"].update(self._create_progress_panel())
        layout["execution_log"].update(self._create_execution_log_panel())
        layout["decision_log"].update(self._create_decision_log_panel())
        layout["footer"].update(self._create_footer())
        
        return layout
        
    def _create_header(self) -> Panel:
        """Create the header panel."""
        active_count = len(self.active_goals)
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        header_text = f"🤖 Enhanced Gemini Computer Control Agent | Active Goals: {active_count} | {timestamp}"
        
        return Panel(
            Text(header_text, style="bold white"),
            style="blue"
        )
        
    def _create_progress_panel(self) -> Panel:
        """Create the progress panel."""
        if not self.active_goals:
            content = Text("No active goals", style="dim")
        else:
            content = self.progress
            
        return Panel(
            content,
            title="Goal Progress",
            border_style="green"
        )
        
    def _create_execution_log_panel(self) -> Panel:
        """Create the execution log panel."""
        if not self.execution_log:
            content = Text("No execution logs", style="dim")
        else:
            # Show last 10 entries
            recent_logs = self.execution_log[-10:]
            content = Text("\n".join(recent_logs))
            
        return Panel(
            content,
            title="Execution Log",
            border_style="yellow"
        )
        
    def _create_decision_log_panel(self) -> Panel:
        """Create the decision log panel."""
        if not self.decision_log:
            content = Text("No AI decisions", style="dim")
        else:
            # Show last 8 entries
            recent_decisions = self.decision_log[-8:]
            content = Text("\n".join(recent_decisions))
            
        return Panel(
            content,
            title="AI Decisions",
            border_style="cyan"
        )
        
    def _create_footer(self) -> Panel:
        """Create the footer panel."""
        footer_text = """
[bold]Controls:[/bold]
• Ctrl+C: Stop current goal
• Ctrl+Shift+Q: Emergency stop (if supported)
• Press 'q' to quit live display

[bold]Status:[/bold] Live feedback active
        """.strip()
        
        return Panel(
            Text(footer_text, style="white"),
            title="Controls & Status",
            border_style="blue"
        )
