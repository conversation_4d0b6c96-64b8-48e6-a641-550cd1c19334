"""
Logging utilities for the Gemini Live Computer Control Agent.
"""

import logging
import os
from datetime import datetime
from typing import Optional
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.console import Console

from config.settings import settings


class AgentLogger:
    """Custom logger for the computer control agent."""
    
    def __init__(self, name: str = "GeminiAgent"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.console = Console()
        self._setup_logger()
    
    def _setup_logger(self):
        """Set up the logger with appropriate handlers and formatting."""
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # Set log level
        log_level = getattr(logging, settings.logging.log_level.upper(), logging.INFO)
        self.logger.setLevel(log_level)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Add console handler with rich formatting
        console_handler = RichHandler(
            console=self.console,
            show_time=True,
            show_path=False,
            rich_tracebacks=True
        )
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # Add file handler if specified
        if settings.logging.log_file:
            # Ensure log directory exists
            log_dir = os.path.dirname(settings.logging.log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            
            file_handler = logging.FileHandler(settings.logging.log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self.logger.info(message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self.logger.debug(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message."""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message."""
        self.logger.critical(message, **kwargs)
    
    def log_action(self, action: str, details: Optional[dict] = None):
        """Log an action taken by the agent."""
        if settings.logging.log_actions:
            timestamp = datetime.now().isoformat()
            log_entry = f"ACTION: {action}"
            if details:
                log_entry += f" | Details: {details}"
            log_entry += f" | Timestamp: {timestamp}"
            self.info(log_entry)
    
    def log_api_call(self, endpoint: str, status: str, details: Optional[dict] = None):
        """Log an API call."""
        if settings.logging.log_api_calls:
            timestamp = datetime.now().isoformat()
            log_entry = f"API_CALL: {endpoint} | Status: {status}"
            if details:
                log_entry += f" | Details: {details}"
            log_entry += f" | Timestamp: {timestamp}"
            self.info(log_entry)
    
    def log_safety_check(self, check_type: str, result: bool, reason: Optional[str] = None):
        """Log a safety check result."""
        timestamp = datetime.now().isoformat()
        status = "PASSED" if result else "FAILED"
        log_entry = f"SAFETY_CHECK: {check_type} | Status: {status}"
        if reason:
            log_entry += f" | Reason: {reason}"
        log_entry += f" | Timestamp: {timestamp}"
        
        if result:
            self.info(log_entry)
        else:
            self.warning(log_entry)


# Global logger instance
logger = AgentLogger()
