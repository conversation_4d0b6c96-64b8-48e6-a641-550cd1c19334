"""Goal validation utilities and safety checks."""

import re
from typing import List, Dict, Any, Optional
from loguru import logger

from ..core.data_models import Goal, ValidationResult, GoalComplexity


class GoalValidator:
    """Advanced goal validation with safety and feasibility checks."""
    
    def __init__(self):
        self.dangerous_keywords = [
            "delete", "remove", "uninstall", "format", "reset", "shutdown", 
            "restart", "reboot", "kill", "terminate", "destroy", "wipe"
        ]
        
        self.system_paths = [
            "system32", "program files", "windows", "applications", 
            "/usr", "/bin", "/sbin", "/etc", "registry"
        ]
        
        self.network_keywords = [
            "download", "upload", "email", "send", "share", "publish",
            "post", "connect", "network", "internet", "wifi"
        ]
        
    def validate_goal_safety(self, goal: Goal) -> ValidationResult:
        """
        Perform comprehensive safety validation of a goal.
        
        Args:
            goal: Goal to validate
            
        Returns:
            ValidationResult with safety assessment
        """
        result = ValidationResult(is_valid=True, confidence=1.0)
        
        # Check for dangerous operations
        dangerous_ops = self._check_dangerous_operations(goal.description)
        if dangerous_ops:
            result.safety_concerns.extend(dangerous_ops)
            result.confidence *= 0.5
            
        # Check for system file access
        system_access = self._check_system_access(goal.description)
        if system_access:
            result.safety_concerns.extend(system_access)
            result.confidence *= 0.6
            
        # Check for network operations
        network_ops = self._check_network_operations(goal.description)
        if network_ops:
            result.warnings.extend(network_ops)
            result.confidence *= 0.8
            
        # Validate sub-tasks
        for task in goal.sub_tasks:
            task_issues = self._validate_task_safety(task.description, task.commands)
            if task_issues:
                result.warnings.extend(task_issues)
                result.confidence *= 0.9
                
        # Check overall complexity vs safety
        if goal.complexity == GoalComplexity.COMPLEX and result.safety_concerns:
            result.warnings.append(
                "Complex goal with safety concerns - consider breaking into smaller goals"
            )
            
        return result
        
    def validate_goal_feasibility(self, goal: Goal) -> ValidationResult:
        """
        Check if a goal is technically feasible.
        
        Args:
            goal: Goal to validate
            
        Returns:
            ValidationResult with feasibility assessment
        """
        result = ValidationResult(is_valid=True, confidence=0.8)
        
        # Check for vague or impossible goals
        vague_indicators = ["somehow", "magically", "automatically", "perfectly"]
        if any(indicator in goal.description.lower() for indicator in vague_indicators):
            result.warnings.append("Goal contains vague language - may be difficult to execute")
            result.confidence *= 0.7
            
        # Check for unrealistic time expectations
        if goal.estimated_duration < 5 and goal.complexity == GoalComplexity.COMPLEX:
            result.warnings.append("Complex goal may need more time than estimated")
            result.confidence *= 0.8
            
        # Check for missing dependencies
        dependency_issues = self._check_dependencies(goal)
        if dependency_issues:
            result.warnings.extend(dependency_issues)
            result.confidence *= 0.9
            
        # Check for conflicting requirements
        conflicts = self._check_conflicts(goal)
        if conflicts:
            result.issues.extend(conflicts)
            result.confidence *= 0.6
            
        return result
        
    def _check_dangerous_operations(self, text: str) -> List[str]:
        """Check for dangerous operations in goal text."""
        concerns = []
        text_lower = text.lower()
        
        for keyword in self.dangerous_keywords:
            if keyword in text_lower:
                concerns.append(f"Dangerous operation detected: '{keyword}'")
                
        return concerns
        
    def _check_system_access(self, text: str) -> List[str]:
        """Check for system file/directory access."""
        concerns = []
        text_lower = text.lower()
        
        for path in self.system_paths:
            if path in text_lower:
                concerns.append(f"System path access detected: '{path}'")
                
        return concerns
        
    def _check_network_operations(self, text: str) -> List[str]:
        """Check for network operations."""
        warnings = []
        text_lower = text.lower()
        
        for keyword in self.network_keywords:
            if keyword in text_lower:
                warnings.append(f"Network operation detected: '{keyword}'")
                
        return warnings
        
    def _validate_task_safety(self, description: str, commands: List[Dict[str, Any]]) -> List[str]:
        """Validate safety of individual task commands."""
        issues = []
        
        for command in commands:
            action = command.get("action", "")
            
            # Check for dangerous actions
            if action in self.dangerous_keywords:
                issues.append(f"Dangerous command action: '{action}'")
                
            # Check for system file operations
            if "target" in command:
                target = str(command["target"]).lower()
                for path in self.system_paths:
                    if path in target:
                        issues.append(f"System file operation: '{action}' on '{target}'")
                        
        return issues
        
    def _check_dependencies(self, goal: Goal) -> List[str]:
        """Check for missing dependencies in goal execution."""
        warnings = []
        
        # Check if goal requires specific applications
        app_requirements = self._extract_app_requirements(goal.description)
        if app_requirements:
            warnings.append(f"Goal requires applications: {', '.join(app_requirements)}")
            
        # Check for file dependencies
        file_requirements = self._extract_file_requirements(goal.description)
        if file_requirements:
            warnings.append(f"Goal may require files: {', '.join(file_requirements)}")
            
        return warnings
        
    def _check_conflicts(self, goal: Goal) -> List[str]:
        """Check for conflicting requirements within a goal."""
        conflicts = []
        
        # Check for contradictory actions
        actions = []
        for task in goal.sub_tasks:
            for command in task.commands:
                actions.append(command.get("action", ""))
                
        # Look for create/delete conflicts
        if "create" in actions and "delete" in actions:
            conflicts.append("Goal contains both create and delete operations")
            
        return conflicts
        
    def _extract_app_requirements(self, text: str) -> List[str]:
        """Extract application requirements from goal text."""
        apps = []
        text_lower = text.lower()
        
        app_patterns = {
            "calculator": ["calculator", "calc"],
            "text_editor": ["notepad", "textedit", "editor", "vim", "code"],
            "web_browser": ["browser", "chrome", "firefox", "safari", "edge"],
            "file_manager": ["finder", "explorer", "files"],
            "spreadsheet": ["excel", "calc", "numbers", "spreadsheet"]
        }
        
        for app_type, keywords in app_patterns.items():
            if any(keyword in text_lower for keyword in keywords):
                apps.append(app_type)
                
        return apps
        
    def _extract_file_requirements(self, text: str) -> List[str]:
        """Extract file requirements from goal text."""
        files = []
        
        # Look for file extensions
        file_patterns = [
            r"\.pdf", r"\.doc", r"\.txt", r"\.jpg", r"\.png", 
            r"\.mp4", r"\.mp3", r"\.zip", r"\.exe"
        ]
        
        for pattern in file_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                files.append(pattern.replace("\\.", "").replace(".", ""))
                
        return files
