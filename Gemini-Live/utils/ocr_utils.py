"""OCR utility functions and helpers."""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from PIL import Image
import io
from loguru import logger

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False


class OCRUtils:
    """Utility functions for OCR operations."""
    
    @staticmethod
    def preprocess_image_for_ocr(image_data: bytes, enhance: bool = True) -> np.ndarray:
        """
        Preprocess image for better OCR results.
        
        Args:
            image_data: Raw image data
            enhance: Whether to apply enhancement filters
            
        Returns:
            Preprocessed image as numpy array
        """
        try:
            # Convert bytes to PIL Image
            pil_image = Image.open(io.BytesIO(image_data))
            
            # Convert to numpy array
            img_array = np.array(pil_image)
            
            # Convert to grayscale if needed
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
                
            if not enhance:
                return gray
                
            # Apply preprocessing for better OCR
            # 1. Noise reduction
            denoised = cv2.medianBlur(gray, 3)
            
            # 2. Contrast enhancement
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)
            
            # 3. Sharpening
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(enhanced, -1, kernel)
            
            return sharpened
            
        except Exception as e:
            logger.error(f"Error preprocessing image for OCR: {e}")
            return np.array([])
    
    @staticmethod
    def extract_text_regions(image: np.ndarray, min_area: int = 100) -> List[Tuple[int, int, int, int]]:
        """
        Extract potential text regions from image.
        
        Args:
            image: Input image as numpy array
            min_area: Minimum area for text regions
            
        Returns:
            List of bounding boxes (x, y, w, h) for text regions
        """
        try:
            # Apply morphological operations to find text regions
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            
            # Gradient
            gradient = cv2.morphologyEx(image, cv2.MORPH_GRADIENT, kernel)
            
            # Threshold
            _, thresh = cv2.threshold(gradient, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Morphological closing
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (9, 1))
            closed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter contours by area and aspect ratio
            text_regions = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > min_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    
                    # Filter by reasonable text aspect ratios
                    if 0.2 < aspect_ratio < 10:
                        text_regions.append((x, y, w, h))
                        
            return text_regions
            
        except Exception as e:
            logger.error(f"Error extracting text regions: {e}")
            return []
    
    @staticmethod
    def filter_text_by_confidence(text_results: List[Dict[str, Any]], 
                                 min_confidence: float = 0.5) -> List[Dict[str, Any]]:
        """
        Filter OCR results by confidence threshold.
        
        Args:
            text_results: List of OCR results with confidence scores
            min_confidence: Minimum confidence threshold
            
        Returns:
            Filtered list of high-confidence results
        """
        return [
            result for result in text_results
            if result.get("confidence", 0) >= min_confidence
        ]
    
    @staticmethod
    def merge_nearby_text(text_elements: List[Dict[str, Any]], 
                         distance_threshold: int = 20) -> List[Dict[str, Any]]:
        """
        Merge nearby text elements that likely belong together.
        
        Args:
            text_elements: List of text elements with coordinates
            distance_threshold: Maximum distance to consider for merging
            
        Returns:
            List of merged text elements
        """
        if not text_elements:
            return []
            
        merged = []
        used_indices = set()
        
        for i, elem1 in enumerate(text_elements):
            if i in used_indices:
                continue
                
            # Start a new merged element
            merged_text = elem1["text"]
            x1, y1, w1, h1 = elem1["coordinates"]
            min_x, min_y = x1, y1
            max_x, max_y = x1 + w1, y1 + h1
            
            used_indices.add(i)
            
            # Look for nearby elements to merge
            for j, elem2 in enumerate(text_elements[i+1:], i+1):
                if j in used_indices:
                    continue
                    
                x2, y2, w2, h2 = elem2["coordinates"]
                
                # Check if elements are close enough
                distance = min(
                    abs(x1 + w1 - x2),  # Horizontal distance
                    abs(y1 + h1 - y2),  # Vertical distance
                    abs(x2 + w2 - x1),  # Reverse horizontal
                    abs(y2 + h2 - y1)   # Reverse vertical
                )
                
                if distance <= distance_threshold:
                    # Merge the elements
                    merged_text += " " + elem2["text"]
                    min_x = min(min_x, x2)
                    min_y = min(min_y, y2)
                    max_x = max(max_x, x2 + w2)
                    max_y = max(max_y, y2 + h2)
                    used_indices.add(j)
            
            # Add merged element
            merged.append({
                "text": merged_text.strip(),
                "coordinates": (min_x, min_y, max_x - min_x, max_y - min_y),
                "confidence": elem1.get("confidence", 1.0)
            })
            
        return merged
    
    @staticmethod
    def find_text_in_region(image: np.ndarray, region: Tuple[int, int, int, int],
                           ocr_engine: str = "easyocr") -> List[Dict[str, Any]]:
        """
        Extract text from a specific region of the image.
        
        Args:
            image: Input image
            region: Region coordinates (x, y, w, h)
            ocr_engine: OCR engine to use
            
        Returns:
            List of text elements found in the region
        """
        try:
            x, y, w, h = region
            
            # Extract region
            roi = image[y:y+h, x:x+w]
            
            if roi.size == 0:
                return []
                
            # Preprocess region
            processed_roi = OCRUtils.preprocess_image_for_ocr(
                cv2.imencode('.jpg', roi)[1].tobytes()
            )
            
            # Run OCR on region
            if ocr_engine == "easyocr" and EASYOCR_AVAILABLE:
                reader = easyocr.Reader(['en'])
                results = reader.readtext(processed_roi)
                
                text_elements = []
                for (bbox, text, confidence) in results:
                    # Adjust coordinates to global image coordinates
                    bbox_array = np.array(bbox)
                    min_x = int(np.min(bbox_array[:, 0])) + x
                    min_y = int(np.min(bbox_array[:, 1])) + y
                    max_x = int(np.max(bbox_array[:, 0])) + x
                    max_y = int(np.max(bbox_array[:, 1])) + y
                    
                    text_elements.append({
                        "text": text.strip(),
                        "coordinates": (min_x, min_y, max_x - min_x, max_y - min_y),
                        "confidence": confidence
                    })
                    
                return text_elements
                
            elif ocr_engine == "tesseract" and TESSERACT_AVAILABLE:
                # Convert to PIL Image for Tesseract
                pil_roi = Image.fromarray(processed_roi)
                
                # Get text with bounding boxes
                data = pytesseract.image_to_data(pil_roi, output_type=pytesseract.Output.DICT)
                
                text_elements = []
                for i in range(len(data['text'])):
                    text = data['text'][i].strip()
                    confidence = int(data['conf'][i])
                    
                    if text and confidence > 0:
                        # Adjust coordinates to global image coordinates
                        bbox_x = data['left'][i] + x
                        bbox_y = data['top'][i] + y
                        bbox_w = data['width'][i]
                        bbox_h = data['height'][i]
                        
                        text_elements.append({
                            "text": text,
                            "coordinates": (bbox_x, bbox_y, bbox_w, bbox_h),
                            "confidence": confidence / 100.0
                        })
                        
                return text_elements
            
            return []
            
        except Exception as e:
            logger.error(f"Error extracting text from region: {e}")
            return []
    
    @staticmethod
    def detect_text_orientation(image: np.ndarray) -> float:
        """
        Detect text orientation in image.
        
        Args:
            image: Input image
            
        Returns:
            Rotation angle in degrees
        """
        try:
            if TESSERACT_AVAILABLE:
                # Use Tesseract's orientation detection
                pil_image = Image.fromarray(image)
                osd = pytesseract.image_to_osd(pil_image)
                
                # Parse orientation from OSD output
                for line in osd.split('\n'):
                    if 'Rotate:' in line:
                        angle = int(line.split(':')[1].strip())
                        return angle
                        
            return 0.0  # Default: no rotation needed
            
        except Exception as e:
            logger.debug(f"Could not detect text orientation: {e}")
            return 0.0
    
    @staticmethod
    def correct_text_orientation(image: np.ndarray, angle: float) -> np.ndarray:
        """
        Correct text orientation by rotating image.
        
        Args:
            image: Input image
            angle: Rotation angle in degrees
            
        Returns:
            Rotated image
        """
        try:
            if abs(angle) < 1:  # No significant rotation needed
                return image
                
            # Get image center
            height, width = image.shape[:2]
            center = (width // 2, height // 2)
            
            # Create rotation matrix
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            
            # Apply rotation
            rotated = cv2.warpAffine(image, rotation_matrix, (width, height),
                                   flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
            
            return rotated
            
        except Exception as e:
            logger.error(f"Error correcting text orientation: {e}")
            return image
