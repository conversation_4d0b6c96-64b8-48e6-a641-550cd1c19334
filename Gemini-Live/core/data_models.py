"""Data models for goal-oriented computer control."""

import uuid
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from dataclasses_json import dataclass_json


class TaskStatus(Enum):
    """Status of a task or goal."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    ROLLBACK = "rollback"


class GoalComplexity(Enum):
    """Complexity levels for goals."""
    SIMPLE = "simple"      # 1-3 steps, low risk
    MEDIUM = "medium"      # 4-10 steps, moderate risk
    COMPLEX = "complex"    # 11+ steps, high risk


@dataclass_json
@dataclass
class SuccessCriteria:
    """Defines success criteria for goals and tasks."""
    description: str
    validation_type: str  # "screen_text", "file_exists", "application_state", etc.
    expected_value: Any
    tolerance: float = 0.0
    timeout_seconds: float = 30.0


@dataclass_json
@dataclass
class UIElement:
    """Represents a detected UI element."""
    element_type: str  # "button", "text_field", "menu", "window", etc.
    text: str
    coordinates: tuple[int, int, int, int]  # x, y, width, height
    confidence: float
    attributes: Dict[str, Any] = field(default_factory=dict)


@dataclass_json
@dataclass
class TextElement:
    """Represents extracted text from screen."""
    text: str
    coordinates: tuple[int, int, int, int]  # x, y, width, height
    confidence: float
    language: str = "en"


@dataclass_json
@dataclass
class ScreenState:
    """Represents the current state of the screen."""
    timestamp: datetime
    ui_elements: List[UIElement]
    text_elements: List[TextElement]
    active_application: Optional[str] = None
    screen_hash: Optional[str] = None


@dataclass_json
@dataclass
class SubTask:
    """Represents a sub-task within a goal."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    description: str = ""
    commands: List[Dict[str, Any]] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    success_criteria: List[SuccessCriteria] = field(default_factory=list)
    rollback_commands: List[Dict[str, Any]] = field(default_factory=list)
    status: TaskStatus = TaskStatus.PENDING
    estimated_duration: float = 10.0  # seconds
    max_retries: int = 3
    retry_count: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


@dataclass_json
@dataclass
class Goal:
    """Represents a high-level goal to be accomplished."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    description: str = ""
    sub_tasks: List[SubTask] = field(default_factory=list)
    success_criteria: List[SuccessCriteria] = field(default_factory=list)
    complexity: GoalComplexity = GoalComplexity.SIMPLE
    estimated_duration: float = 60.0  # seconds
    max_execution_time: float = 300.0  # 5 minutes default
    status: TaskStatus = TaskStatus.PENDING
    progress: float = 0.0  # 0.0 to 1.0
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    user_confirmation_required: bool = False
    safety_level: str = "standard"  # "low", "standard", "high"
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass_json
@dataclass
class ExecutionPlan:
    """Represents an execution plan for a goal."""
    goal_id: str
    ordered_tasks: List[str]  # Task IDs in execution order
    parallel_groups: List[List[str]] = field(default_factory=list)  # Tasks that can run in parallel
    estimated_total_time: float = 0.0
    risk_assessment: str = "low"  # "low", "medium", "high"
    rollback_plan: List[str] = field(default_factory=list)  # Rollback task IDs
    created_at: datetime = field(default_factory=datetime.now)


@dataclass_json
@dataclass
class GoalMetrics:
    """Performance metrics for goal execution."""
    goal_id: str
    execution_time: float
    success_rate: float
    commands_executed: int
    commands_failed: int
    retries_performed: int
    user_interactions: int
    screen_captures: int
    ocr_operations: int
    ui_detections: int
    memory_usage_mb: float
    cpu_usage_percent: float
    created_at: datetime = field(default_factory=datetime.now)


@dataclass_json
@dataclass
class ExecutionResult:
    """Result of executing a goal or task."""
    goal_id: str
    task_id: Optional[str] = None
    status: TaskStatus = TaskStatus.PENDING
    success: bool = False
    message: str = ""
    execution_time: float = 0.0
    commands_executed: int = 0
    commands_failed: int = 0
    error_details: Optional[str] = None
    metrics: Optional[GoalMetrics] = None
    created_at: datetime = field(default_factory=datetime.now)


@dataclass_json
@dataclass
class ValidationResult:
    """Result of goal or task validation."""
    is_valid: bool
    confidence: float
    issues: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    estimated_complexity: GoalComplexity = GoalComplexity.SIMPLE
    estimated_duration: float = 60.0
    safety_concerns: List[str] = field(default_factory=list)


@dataclass_json
@dataclass
class ChangeDetection:
    """Result of screen change detection."""
    has_changed: bool
    change_percentage: float
    changed_regions: List[tuple[int, int, int, int]] = field(default_factory=list)
    change_type: str = "unknown"  # "ui_change", "content_change", "application_change"
    confidence: float = 0.0
