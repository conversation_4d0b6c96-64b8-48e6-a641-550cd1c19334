"""
Screen capture and analysis module for the Gemini Live Computer Control Agent.
"""

import io
import base64
import time
from typing import Optional, Tu<PERSON>, Dict, Any
from PIL import Image, ImageDraw
import pyautogui
import cv2
import numpy as np

from config.settings import settings
from utils.logger import logger


class ScreenAnalyzer:
    """Handles screen capture, processing, and analysis."""
    
    def __init__(self):
        # Disable pyautogui failsafe for automated operation
        pyautogui.FAILSAFE = True  # Keep failsafe enabled for safety
        pyautogui.PAUSE = 0.1  # Small pause between actions
        
        self.last_screenshot_time = 0
        self.cached_screenshot = None
        
    def capture_screenshot(self, region: Optional[Tuple[int, int, int, int]] = None) -> Image.Image:
        """
        Capture a screenshot of the screen or a specific region.
        
        Args:
            region: Optional tuple (left, top, width, height) for partial capture
            
        Returns:
            PIL Image object
        """
        try:
            current_time = time.time()
            
            # Check if we can use cached screenshot (rate limiting)
            if (current_time - self.last_screenshot_time < settings.performance.screenshot_interval 
                and self.cached_screenshot is not None):
                logger.debug("Using cached screenshot")
                return self.cached_screenshot
            
            # Capture screenshot
            if region:
                screenshot = pyautogui.screenshot(region=region)
            else:
                screenshot = pyautogui.screenshot()
            
            # Resize if necessary
            max_width, max_height = settings.performance.max_image_size
            if screenshot.width > max_width or screenshot.height > max_height:
                screenshot.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
            
            # Cache the screenshot
            self.cached_screenshot = screenshot
            self.last_screenshot_time = current_time
            
            logger.debug(f"Screenshot captured: {screenshot.width}x{screenshot.height}")
            return screenshot
            
        except Exception as e:
            logger.error(f"Failed to capture screenshot: {e}")
            raise
    
    def screenshot_to_base64(self, screenshot: Optional[Image.Image] = None) -> str:
        """
        Convert screenshot to base64 string for API transmission.
        
        Args:
            screenshot: PIL Image object, if None captures new screenshot
            
        Returns:
            Base64 encoded image string
        """
        if screenshot is None:
            screenshot = self.capture_screenshot()
        
        # Convert to RGB if necessary
        if screenshot.mode != 'RGB':
            screenshot = screenshot.convert('RGB')
        
        # Compress image
        buffer = io.BytesIO()
        screenshot.save(
            buffer, 
            format='JPEG', 
            quality=settings.performance.screenshot_quality,
            optimize=True
        )
        
        # Encode to base64
        image_data = buffer.getvalue()
        base64_string = base64.b64encode(image_data).decode('utf-8')
        
        logger.debug(f"Screenshot encoded to base64: {len(base64_string)} characters")
        return base64_string
    
    def get_screen_info(self) -> Dict[str, Any]:
        """
        Get information about the current screen.
        
        Returns:
            Dictionary with screen information
        """
        try:
            screen_size = pyautogui.size()
            mouse_position = pyautogui.position()
            
            info = {
                "screen_width": screen_size.width,
                "screen_height": screen_size.height,
                "mouse_x": mouse_position.x,
                "mouse_y": mouse_position.y,
                "timestamp": time.time()
            }
            
            logger.debug(f"Screen info: {info}")
            return info
            
        except Exception as e:
            logger.error(f"Failed to get screen info: {e}")
            return {}
    
    def find_element_by_image(self, template_path: str, confidence: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Find an element on screen by image template matching.
        
        Args:
            template_path: Path to template image
            confidence: Matching confidence threshold (0.0 to 1.0)
            
        Returns:
            Tuple (x, y) of element center, or None if not found
        """
        try:
            # Capture current screen
            screenshot = self.capture_screenshot()
            
            # Convert PIL to OpenCV format
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # Load template
            template = cv2.imread(template_path)
            if template is None:
                logger.error(f"Could not load template image: {template_path}")
                return None
            
            # Perform template matching
            result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= confidence:
                # Calculate center of matched region
                template_height, template_width = template.shape[:2]
                center_x = max_loc[0] + template_width // 2
                center_y = max_loc[1] + template_height // 2
                
                logger.debug(f"Element found at ({center_x}, {center_y}) with confidence {max_val}")
                return (center_x, center_y)
            else:
                logger.debug(f"Element not found, best match confidence: {max_val}")
                return None
                
        except Exception as e:
            logger.error(f"Error in image matching: {e}")
            return None
    
    def annotate_screenshot(self, screenshot: Image.Image, annotations: list) -> Image.Image:
        """
        Add annotations to a screenshot for debugging/visualization.
        
        Args:
            screenshot: PIL Image object
            annotations: List of annotation dictionaries
            
        Returns:
            Annotated PIL Image
        """
        annotated = screenshot.copy()
        draw = ImageDraw.Draw(annotated)
        
        for annotation in annotations:
            ann_type = annotation.get('type', 'point')
            color = annotation.get('color', 'red')
            
            if ann_type == 'point':
                x, y = annotation['coordinates']
                # Draw a small circle
                radius = 5
                draw.ellipse([x-radius, y-radius, x+radius, y+radius], outline=color, width=2)
                
            elif ann_type == 'rectangle':
                x1, y1, x2, y2 = annotation['coordinates']
                draw.rectangle([x1, y1, x2, y2], outline=color, width=2)
                
            elif ann_type == 'text':
                x, y = annotation['coordinates']
                text = annotation['text']
                draw.text((x, y), text, fill=color)
        
        return annotated
    
    def get_pixel_color(self, x: int, y: int) -> Tuple[int, int, int]:
        """
        Get the RGB color of a pixel at the specified coordinates.
        
        Args:
            x, y: Screen coordinates
            
        Returns:
            RGB tuple
        """
        try:
            screenshot = self.capture_screenshot()
            color = screenshot.getpixel((x, y))
            logger.debug(f"Pixel color at ({x}, {y}): {color}")
            return color
        except Exception as e:
            logger.error(f"Failed to get pixel color: {e}")
            return (0, 0, 0)


# Global screen analyzer instance
screen_analyzer = ScreenAnalyzer()
