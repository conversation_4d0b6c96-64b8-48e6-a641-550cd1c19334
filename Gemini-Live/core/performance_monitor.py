"""Performance monitoring and optimization for goal execution."""

import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from loguru import logger

from .data_models import Goal, GoalMetrics, ExecutionResult, TaskStatus


@dataclass
class PerformanceSnapshot:
    """Snapshot of system performance at a point in time."""
    timestamp: datetime
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    active_goals: int
    commands_per_second: float


class PerformanceMonitor:
    """Monitors and optimizes goal execution performance."""
    
    def __init__(self, monitoring_interval: float = 10.0):
        """
        Initialize the performance monitor.
        
        Args:
            monitoring_interval: Interval between performance snapshots (seconds)
        """
        self.monitoring_interval = monitoring_interval
        self.performance_history: List[PerformanceSnapshot] = []
        self.goal_metrics: Dict[str, GoalMetrics] = {}
        self.execution_times: Dict[str, List[float]] = {}  # goal_type -> execution_times
        self.command_counts: Dict[str, int] = {}  # command_type -> count
        self.error_counts: Dict[str, int] = {}  # error_type -> count
        
        # Performance thresholds
        self.cpu_threshold = 80.0  # %
        self.memory_threshold = 85.0  # %
        self.response_time_threshold = 5.0  # seconds
        
        # Monitoring state
        self.monitoring_active = False
        self.start_time = None
        self.last_snapshot_time = None
        
    def start_monitoring(self) -> None:
        """Start performance monitoring."""
        self.monitoring_active = True
        self.start_time = datetime.now()
        self.last_snapshot_time = self.start_time
        logger.info("Performance monitoring started")
        
    def stop_monitoring(self) -> None:
        """Stop performance monitoring."""
        self.monitoring_active = False
        logger.info("Performance monitoring stopped")
        
    def record_goal_execution(self, goal: Goal, execution_result: ExecutionResult) -> GoalMetrics:
        """
        Record performance metrics for a goal execution.
        
        Args:
            goal: Goal that was executed
            execution_result: Result of the execution
            
        Returns:
            GoalMetrics object with performance data
        """
        # Get current system metrics
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory_info = psutil.virtual_memory()
        memory_mb = memory_info.used / (1024 * 1024)
        
        # Calculate performance metrics
        metrics = GoalMetrics(
            goal_id=goal.id,
            execution_time=execution_result.execution_time,
            success_rate=1.0 if execution_result.success else 0.0,
            commands_executed=execution_result.commands_executed,
            commands_failed=execution_result.commands_failed,
            retries_performed=self._count_retries(goal),
            user_interactions=self._count_user_interactions(goal),
            screen_captures=self._estimate_screen_captures(execution_result.execution_time),
            ocr_operations=self._estimate_ocr_operations(execution_result.execution_time),
            ui_detections=self._estimate_ui_detections(execution_result.execution_time),
            memory_usage_mb=memory_mb,
            cpu_usage_percent=cpu_percent
        )
        
        # Store metrics
        self.goal_metrics[goal.id] = metrics
        
        # Update execution time tracking
        goal_type = goal.complexity.value
        if goal_type not in self.execution_times:
            self.execution_times[goal_type] = []
        self.execution_times[goal_type].append(execution_result.execution_time)
        
        # Update command tracking
        for task in goal.sub_tasks:
            for command in task.commands:
                action = command.get("action", "unknown")
                self.command_counts[action] = self.command_counts.get(action, 0) + 1
                
        logger.debug(f"Recorded performance metrics for goal: {goal.id}")
        return metrics
        
    def take_performance_snapshot(self, active_goals_count: int = 0) -> PerformanceSnapshot:
        """
        Take a snapshot of current system performance.
        
        Args:
            active_goals_count: Number of currently active goals
            
        Returns:
            PerformanceSnapshot with current metrics
        """
        # Get system metrics
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory_info = psutil.virtual_memory()
        memory_mb = memory_info.used / (1024 * 1024)
        memory_percent = memory_info.percent
        
        # Calculate commands per second
        commands_per_second = self._calculate_commands_per_second()
        
        snapshot = PerformanceSnapshot(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_mb=memory_mb,
            memory_percent=memory_percent,
            active_goals=active_goals_count,
            commands_per_second=commands_per_second
        )
        
        # Store snapshot
        self.performance_history.append(snapshot)
        
        # Limit history size
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-500:]
            
        self.last_snapshot_time = snapshot.timestamp
        
        # Check for performance issues
        self._check_performance_thresholds(snapshot)
        
        return snapshot
        
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get a performance summary for the specified time period.
        
        Args:
            hours: Number of hours to include in summary
            
        Returns:
            Dictionary with performance summary
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Filter recent snapshots
        recent_snapshots = [
            s for s in self.performance_history
            if s.timestamp >= cutoff_time
        ]
        
        if not recent_snapshots:
            return {"error": "No performance data available"}
            
        # Calculate averages
        avg_cpu = sum(s.cpu_percent for s in recent_snapshots) / len(recent_snapshots)
        avg_memory = sum(s.memory_percent for s in recent_snapshots) / len(recent_snapshots)
        avg_commands_per_sec = sum(s.commands_per_second for s in recent_snapshots) / len(recent_snapshots)
        
        # Find peaks
        max_cpu = max(s.cpu_percent for s in recent_snapshots)
        max_memory = max(s.memory_percent for s in recent_snapshots)
        
        # Calculate goal execution statistics
        recent_goals = [
            metrics for metrics in self.goal_metrics.values()
            if metrics.created_at >= cutoff_time
        ]
        
        total_goals = len(recent_goals)
        successful_goals = sum(1 for m in recent_goals if m.success_rate == 1.0)
        success_rate = successful_goals / total_goals if total_goals > 0 else 0.0
        
        avg_execution_time = (
            sum(m.execution_time for m in recent_goals) / total_goals
            if total_goals > 0 else 0.0
        )
        
        return {
            "time_period_hours": hours,
            "system_performance": {
                "avg_cpu_percent": avg_cpu,
                "max_cpu_percent": max_cpu,
                "avg_memory_percent": avg_memory,
                "max_memory_percent": max_memory,
                "avg_commands_per_second": avg_commands_per_sec
            },
            "goal_execution": {
                "total_goals": total_goals,
                "successful_goals": successful_goals,
                "success_rate": success_rate,
                "avg_execution_time": avg_execution_time
            },
            "command_statistics": dict(self.command_counts),
            "error_statistics": dict(self.error_counts)
        }
        
    def get_optimization_recommendations(self) -> List[str]:
        """
        Get performance optimization recommendations.
        
        Returns:
            List of optimization recommendations
        """
        recommendations = []
        
        # Check recent performance
        recent_snapshots = self.performance_history[-10:] if self.performance_history else []
        
        if recent_snapshots:
            avg_cpu = sum(s.cpu_percent for s in recent_snapshots) / len(recent_snapshots)
            avg_memory = sum(s.memory_percent for s in recent_snapshots) / len(recent_snapshots)
            
            if avg_cpu > self.cpu_threshold:
                recommendations.append(
                    f"High CPU usage detected ({avg_cpu:.1f}%). "
                    "Consider reducing screen capture frequency or goal complexity."
                )
                
            if avg_memory > self.memory_threshold:
                recommendations.append(
                    f"High memory usage detected ({avg_memory:.1f}%). "
                    "Consider clearing goal history or reducing concurrent goals."
                )
                
        # Check execution time patterns
        for goal_type, times in self.execution_times.items():
            if len(times) >= 5:
                avg_time = sum(times) / len(times)
                if avg_time > self.response_time_threshold:
                    recommendations.append(
                        f"Slow execution times for {goal_type} goals ({avg_time:.1f}s average). "
                        "Consider breaking down complex goals into smaller tasks."
                    )
                    
        # Check error patterns
        total_commands = sum(self.command_counts.values())
        total_errors = sum(self.error_counts.values())
        
        if total_commands > 0:
            error_rate = total_errors / total_commands
            if error_rate > 0.1:  # 10% error rate
                recommendations.append(
                    f"High error rate detected ({error_rate:.1%}). "
                    "Review command validation and safety settings."
                )
                
        # Check command efficiency
        inefficient_commands = [
            cmd for cmd, count in self.command_counts.items()
            if count > 100 and cmd in ["wait", "retry", "analyze_screen"]
        ]
        
        if inefficient_commands:
            recommendations.append(
                f"High usage of potentially inefficient commands: {', '.join(inefficient_commands)}. "
                "Consider optimizing goal planning."
            )
            
        if not recommendations:
            recommendations.append("Performance looks good! No optimization recommendations at this time.")
            
        return recommendations
        
    def benchmark_goal_type(self, goal_type: str, sample_size: int = 10) -> Dict[str, float]:
        """
        Benchmark performance for a specific goal type.
        
        Args:
            goal_type: Type of goal to benchmark
            sample_size: Number of recent executions to include
            
        Returns:
            Dictionary with benchmark results
        """
        if goal_type not in self.execution_times:
            return {"error": f"No execution data for goal type: {goal_type}"}
            
        times = self.execution_times[goal_type][-sample_size:]
        
        if not times:
            return {"error": f"No recent execution data for goal type: {goal_type}"}
            
        return {
            "goal_type": goal_type,
            "sample_size": len(times),
            "avg_execution_time": sum(times) / len(times),
            "min_execution_time": min(times),
            "max_execution_time": max(times),
            "median_execution_time": sorted(times)[len(times) // 2]
        }
        
    def record_error(self, error_type: str, error_message: str) -> None:
        """
        Record an error for performance tracking.
        
        Args:
            error_type: Type of error
            error_message: Error message
        """
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        logger.debug(f"Recorded error: {error_type}")
        
    def _count_retries(self, goal: Goal) -> int:
        """Count the number of retries performed for a goal."""
        return sum(task.retry_count for task in goal.sub_tasks)
        
    def _count_user_interactions(self, goal: Goal) -> int:
        """Count user interactions for a goal."""
        # This would be enhanced with actual interaction tracking
        return sum(
            1 for task in goal.sub_tasks
            for command in task.commands
            if command.get("action") == "request_input"
        )
        
    def _estimate_screen_captures(self, execution_time: float) -> int:
        """Estimate number of screen captures based on execution time."""
        # Assume 1 capture per 2 seconds on average
        return int(execution_time / 2)
        
    def _estimate_ocr_operations(self, execution_time: float) -> int:
        """Estimate number of OCR operations based on execution time."""
        # Assume OCR on 50% of screen captures
        return int(self._estimate_screen_captures(execution_time) * 0.5)
        
    def _estimate_ui_detections(self, execution_time: float) -> int:
        """Estimate number of UI detections based on execution time."""
        # Assume UI detection on 80% of screen captures
        return int(self._estimate_screen_captures(execution_time) * 0.8)
        
    def _calculate_commands_per_second(self) -> float:
        """Calculate recent commands per second rate."""
        if not self.last_snapshot_time:
            return 0.0
            
        time_diff = (datetime.now() - self.last_snapshot_time).total_seconds()
        if time_diff <= 0:
            return 0.0
            
        # Count commands in the last interval
        recent_commands = sum(
            count for count in self.command_counts.values()
        ) if self.command_counts else 0
        
        return recent_commands / time_diff if time_diff > 0 else 0.0
        
    def _check_performance_thresholds(self, snapshot: PerformanceSnapshot) -> None:
        """Check if performance thresholds are exceeded."""
        if snapshot.cpu_percent > self.cpu_threshold:
            logger.warning(f"High CPU usage: {snapshot.cpu_percent:.1f}%")
            
        if snapshot.memory_percent > self.memory_threshold:
            logger.warning(f"High memory usage: {snapshot.memory_percent:.1f}%")
