"""Task planning and execution engine."""

import asyncio
from datetime import datetime
from typing import List, Dict, Optional, Any
from loguru import logger

from .data_models import (
    Goal, SubTask, ExecutionPlan, ExecutionResult, TaskStatus, SuccessCriteria
)


class TaskPlanner:
    """Plans and executes multi-step tasks with error recovery."""
    
    def __init__(self):
        self.active_plans = {}  # goal_id -> ExecutionPlan
        self.execution_results = {}  # goal_id -> List[ExecutionResult]
        self.rollback_stack = {}  # goal_id -> List[SubTask]
        
    def create_execution_plan(self, goal: Goal) -> ExecutionPlan:
        """
        Create an execution plan for a goal.
        
        Args:
            goal: Goal to create plan for
            
        Returns:
            ExecutionPlan with ordered tasks and dependencies
        """
        logger.info(f"Creating execution plan for goal: {goal.id}")
        
        # Analyze task dependencies
        ordered_tasks = self._resolve_dependencies(goal.sub_tasks)
        
        # Identify parallel execution opportunities
        parallel_groups = self._identify_parallel_tasks(goal.sub_tasks, ordered_tasks)
        
        # Estimate total execution time
        total_time = sum(task.estimated_duration for task in goal.sub_tasks)
        
        # Assess risk level
        risk_level = self._assess_execution_risk(goal)
        
        # Create rollback plan
        rollback_plan = self._create_rollback_plan(goal.sub_tasks)
        
        plan = ExecutionPlan(
            goal_id=goal.id,
            ordered_tasks=ordered_tasks,
            parallel_groups=parallel_groups,
            estimated_total_time=total_time,
            risk_assessment=risk_level,
            rollback_plan=rollback_plan
        )
        
        self.active_plans[goal.id] = plan
        logger.info(f"Created execution plan: {len(ordered_tasks)} tasks, "
                   f"risk: {risk_level}, time: {total_time:.1f}s")
        
        return plan
    
    async def execute_plan(self, plan: ExecutionPlan, goal: Goal, 
                          computer_controller, step_by_step: bool = False) -> ExecutionResult:
        """
        Execute an execution plan.
        
        Args:
            plan: ExecutionPlan to execute
            goal: Goal being executed
            computer_controller: Controller for executing commands
            step_by_step: Whether to ask for confirmation between steps
            
        Returns:
            ExecutionResult with execution details
        """
        logger.info(f"Executing plan for goal: {plan.goal_id}")
        
        start_time = datetime.now()
        executed_commands = 0
        failed_commands = 0
        
        try:
            # Initialize execution tracking
            self.execution_results[plan.goal_id] = []
            self.rollback_stack[plan.goal_id] = []
            
            # Execute tasks in order
            for task_id in plan.ordered_tasks:
                task = self._find_task_by_id(goal.sub_tasks, task_id)
                if not task:
                    logger.error(f"Task not found: {task_id}")
                    continue
                    
                # Check if step-by-step confirmation is needed
                if step_by_step:
                    confirmation = await self._request_step_confirmation(task)
                    if not confirmation:
                        logger.info(f"Task skipped by user: {task.id}")
                        continue
                        
                # Execute the task
                task_result = await self._execute_task(task, computer_controller)
                
                # Track results
                self.execution_results[plan.goal_id].append(task_result)
                executed_commands += task_result.commands_executed
                failed_commands += task_result.commands_failed
                
                # Handle task failure
                if not task_result.success:
                    recovery_action = await self.handle_task_failure(task, task_result, computer_controller)
                    if recovery_action == "abort":
                        break
                    elif recovery_action == "rollback":
                        await self._execute_rollback(plan.goal_id, computer_controller)
                        break
                        
                # Add to rollback stack if task has rollback commands
                if task.rollback_commands:
                    self.rollback_stack[plan.goal_id].append(task)
                    
            # Calculate final result
            execution_time = (datetime.now() - start_time).total_seconds()
            success = failed_commands == 0
            
            result = ExecutionResult(
                goal_id=plan.goal_id,
                status=TaskStatus.COMPLETED if success else TaskStatus.FAILED,
                success=success,
                message=f"Executed {executed_commands} commands, {failed_commands} failed",
                execution_time=execution_time,
                commands_executed=executed_commands,
                commands_failed=failed_commands
            )
            
            logger.info(f"Plan execution completed: {result.message}")
            return result
            
        except Exception as e:
            logger.error(f"Error executing plan: {e}")
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return ExecutionResult(
                goal_id=plan.goal_id,
                status=TaskStatus.FAILED,
                success=False,
                message=f"Execution failed: {str(e)}",
                execution_time=execution_time,
                error_details=str(e)
            )
    
    async def handle_task_failure(self, task: SubTask, result: ExecutionResult, 
                                 computer_controller) -> str:
        """
        Handle task failure with recovery strategies.
        
        Args:
            task: Failed task
            result: Execution result with error details
            computer_controller: Controller for executing recovery commands
            
        Returns:
            Recovery action: "retry", "skip", "rollback", or "abort"
        """
        logger.warning(f"Task failed: {task.id} - {result.error_details}")
        
        # Check retry count
        if task.retry_count < task.max_retries:
            task.retry_count += 1
            logger.info(f"Retrying task {task.id} (attempt {task.retry_count}/{task.max_retries})")
            
            # Wait before retry
            await asyncio.sleep(2.0)
            
            # Re-execute the task
            retry_result = await self._execute_task(task, computer_controller)
            if retry_result.success:
                logger.info(f"Task retry successful: {task.id}")
                return "continue"
            else:
                logger.warning(f"Task retry failed: {task.id}")
                
        # Determine recovery strategy based on task type and error
        if "permission" in result.error_details.lower():
            logger.error("Permission error - aborting execution")
            return "abort"
        elif "timeout" in result.error_details.lower():
            logger.warning("Timeout error - attempting rollback")
            return "rollback"
        elif task.retry_count >= task.max_retries:
            logger.warning("Max retries reached - skipping task")
            return "skip"
        else:
            return "retry"
    
    async def rollback_task(self, task: SubTask, computer_controller) -> bool:
        """
        Rollback a specific task.
        
        Args:
            task: Task to rollback
            computer_controller: Controller for executing rollback commands
            
        Returns:
            True if rollback successful, False otherwise
        """
        if not task.rollback_commands:
            logger.info(f"No rollback commands for task: {task.id}")
            return True
            
        logger.info(f"Rolling back task: {task.id}")
        
        try:
            # Execute rollback commands in reverse order
            for command in reversed(task.rollback_commands):
                success = await computer_controller.execute_command(command)
                if not success:
                    logger.warning(f"Rollback command failed: {command}")
                    return False
                    
            logger.info(f"Task rollback successful: {task.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error during task rollback: {e}")
            return False
    
    def _resolve_dependencies(self, tasks: List[SubTask]) -> List[str]:
        """Resolve task dependencies and return ordered task IDs."""
        ordered = []
        remaining = {task.id: task for task in tasks}
        
        while remaining:
            # Find tasks with no unresolved dependencies
            ready_tasks = []
            for task_id, task in remaining.items():
                if all(dep_id in ordered for dep_id in task.dependencies):
                    ready_tasks.append(task_id)
                    
            if not ready_tasks:
                # Circular dependency or missing dependency
                logger.warning("Circular or missing dependencies detected, adding remaining tasks")
                ready_tasks = list(remaining.keys())
                
            # Add ready tasks to order
            for task_id in ready_tasks:
                ordered.append(task_id)
                del remaining[task_id]
                
        return ordered
    
    def _identify_parallel_tasks(self, tasks: List[SubTask], ordered_tasks: List[str]) -> List[List[str]]:
        """Identify tasks that can be executed in parallel."""
        parallel_groups = []
        task_dict = {task.id: task for task in tasks}
        
        # Simple approach: tasks with no dependencies can run in parallel
        independent_tasks = []
        for task_id in ordered_tasks:
            task = task_dict[task_id]
            if not task.dependencies:
                independent_tasks.append(task_id)
                
        if len(independent_tasks) > 1:
            parallel_groups.append(independent_tasks)
            
        return parallel_groups
    
    def _assess_execution_risk(self, goal: Goal) -> str:
        """Assess the risk level of executing a goal."""
        risk_factors = 0
        
        # Check for high-risk operations
        for task in goal.sub_tasks:
            for command in task.commands:
                action = command.get("action", "")
                if action in ["delete", "remove", "format", "shutdown"]:
                    risk_factors += 3
                elif action in ["install", "download", "modify"]:
                    risk_factors += 1
                    
        # Check goal complexity
        if goal.complexity.value == "complex":
            risk_factors += 2
        elif goal.complexity.value == "medium":
            risk_factors += 1
            
        # Check execution time
        if goal.estimated_duration > 300:  # 5 minutes
            risk_factors += 1
            
        # Determine risk level
        if risk_factors >= 5:
            return "high"
        elif risk_factors >= 2:
            return "medium"
        else:
            return "low"
    
    def _create_rollback_plan(self, tasks: List[SubTask]) -> List[str]:
        """Create a rollback plan for tasks."""
        rollback_order = []
        
        # Rollback in reverse dependency order
        for task in reversed(tasks):
            if task.rollback_commands:
                rollback_order.append(task.id)
                
        return rollback_order
    
    def _find_task_by_id(self, tasks: List[SubTask], task_id: str) -> Optional[SubTask]:
        """Find a task by its ID."""
        for task in tasks:
            if task.id == task_id:
                return task
        return None
    
    async def _execute_task(self, task: SubTask, computer_controller) -> ExecutionResult:
        """Execute a single task."""
        logger.info(f"Executing task: {task.description}")
        
        start_time = datetime.now()
        task.status = TaskStatus.IN_PROGRESS
        task.started_at = start_time
        
        executed_count = 0
        failed_count = 0
        
        try:
            # Execute each command in the task
            for command in task.commands:
                success = await computer_controller.execute_command(command)
                if success:
                    executed_count += 1
                else:
                    failed_count += 1
                    
            # Check success criteria
            success = failed_count == 0 and await self._validate_success_criteria(task.success_criteria)
            
            # Update task status
            task.status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
            task.completed_at = datetime.now()
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            result = ExecutionResult(
                goal_id="",  # Will be set by caller
                task_id=task.id,
                status=task.status,
                success=success,
                message=f"Task {'completed' if success else 'failed'}",
                execution_time=execution_time,
                commands_executed=executed_count,
                commands_failed=failed_count
            )
            
            if not success:
                result.error_details = f"{failed_count} commands failed"
                
            return result
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error_message = str(e)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return ExecutionResult(
                goal_id="",
                task_id=task.id,
                status=TaskStatus.FAILED,
                success=False,
                message=f"Task execution error: {str(e)}",
                execution_time=execution_time,
                error_details=str(e)
            )
    
    async def _validate_success_criteria(self, criteria: List[SuccessCriteria]) -> bool:
        """Validate success criteria for a task."""
        # Placeholder implementation
        # In a full implementation, this would check each criterion
        return True
    
    async def _request_step_confirmation(self, task: SubTask) -> bool:
        """Request user confirmation for step-by-step execution."""
        # Placeholder implementation
        # In a full implementation, this would prompt the user
        logger.info(f"Step confirmation requested for: {task.description}")
        return True
    
    async def _execute_rollback(self, goal_id: str, computer_controller) -> None:
        """Execute rollback for all tasks in the rollback stack."""
        if goal_id not in self.rollback_stack:
            return
            
        logger.info(f"Executing rollback for goal: {goal_id}")
        
        # Rollback tasks in reverse order
        for task in reversed(self.rollback_stack[goal_id]):
            await self.rollback_task(task, computer_controller)
            
        # Clear rollback stack
        self.rollback_stack[goal_id] = []
