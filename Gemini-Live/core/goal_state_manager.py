"""Goal state management and progress tracking."""

import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger

from .data_models import Goal, SubTask, TaskStatus, GoalMetrics, ExecutionResult


class GoalStateManager:
    """Manages goal state, progress tracking, and persistence."""
    
    def __init__(self, state_file: Optional[str] = None):
        """
        Initialize the goal state manager.
        
        Args:
            state_file: Path to state persistence file (optional)
        """
        self.state_file = Path(state_file) if state_file else Path("goal_state.json")
        self.active_goals: Dict[str, Goal] = {}
        self.completed_goals: Dict[str, Goal] = {}
        self.goal_metrics: Dict[str, GoalMetrics] = {}
        self.execution_history: Dict[str, List[ExecutionResult]] = {}
        
        # Load existing state
        self._load_state()
        
    def track_goal_progress(self, goal: Goal, progress: float) -> None:
        """
        Track progress for a goal.
        
        Args:
            goal: Goal to track
            progress: Progress value (0.0 to 1.0)
        """
        goal.progress = max(0.0, min(1.0, progress))
        
        # Update goal in active goals
        self.active_goals[goal.id] = goal
        
        logger.debug(f"Goal progress updated: {goal.id} -> {progress:.1%}")
        
        # Auto-save state
        self._save_state()
        
    def update_task_status(self, goal_id: str, task: SubTask, status: TaskStatus) -> None:
        """
        Update the status of a task within a goal.
        
        Args:
            goal_id: ID of the goal containing the task
            task: Task to update
            status: New status for the task
        """
        task.status = status
        
        if status == TaskStatus.IN_PROGRESS and not task.started_at:
            task.started_at = datetime.now()
        elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            task.completed_at = datetime.now()
            
        # Update goal progress based on task completion
        if goal_id in self.active_goals:
            goal = self.active_goals[goal_id]
            self._update_goal_progress(goal)
            
        logger.debug(f"Task status updated: {task.id} -> {status.value}")
        
        # Auto-save state
        self._save_state()
        
    def check_goal_completion(self, goal: Goal) -> bool:
        """
        Check if a goal is completed based on its tasks and success criteria.
        
        Args:
            goal: Goal to check
            
        Returns:
            True if goal is completed, False otherwise
        """
        # Check if all tasks are completed
        all_tasks_completed = all(
            task.status == TaskStatus.COMPLETED 
            for task in goal.sub_tasks
        )
        
        if all_tasks_completed:
            # Mark goal as completed
            goal.status = TaskStatus.COMPLETED
            goal.completed_at = datetime.now()
            goal.progress = 1.0
            
            # Move from active to completed
            if goal.id in self.active_goals:
                del self.active_goals[goal.id]
                self.completed_goals[goal.id] = goal
                
            logger.info(f"Goal completed: {goal.id}")
            self._save_state()
            return True
            
        # Check if goal has failed
        failed_tasks = [task for task in goal.sub_tasks if task.status == TaskStatus.FAILED]
        if failed_tasks and len(failed_tasks) > len(goal.sub_tasks) // 2:
            # More than half the tasks failed
            goal.status = TaskStatus.FAILED
            goal.completed_at = datetime.now()
            
            if goal.id in self.active_goals:
                del self.active_goals[goal.id]
                self.completed_goals[goal.id] = goal
                
            logger.warning(f"Goal failed: {goal.id}")
            self._save_state()
            
        return False
        
    def get_goal_metrics(self, goal_id: str) -> Optional[GoalMetrics]:
        """
        Get performance metrics for a goal.
        
        Args:
            goal_id: ID of the goal
            
        Returns:
            GoalMetrics object or None if not found
        """
        return self.goal_metrics.get(goal_id)
        
    def update_goal_metrics(self, goal_id: str, metrics: GoalMetrics) -> None:
        """
        Update performance metrics for a goal.
        
        Args:
            goal_id: ID of the goal
            metrics: Updated metrics
        """
        self.goal_metrics[goal_id] = metrics
        logger.debug(f"Goal metrics updated: {goal_id}")
        self._save_state()
        
    def add_execution_result(self, goal_id: str, result: ExecutionResult) -> None:
        """
        Add an execution result to the history.
        
        Args:
            goal_id: ID of the goal
            result: Execution result to add
        """
        if goal_id not in self.execution_history:
            self.execution_history[goal_id] = []
            
        self.execution_history[goal_id].append(result)
        
        # Limit history size
        if len(self.execution_history[goal_id]) > 100:
            self.execution_history[goal_id] = self.execution_history[goal_id][-50:]
            
        logger.debug(f"Execution result added for goal: {goal_id}")
        
    def get_execution_history(self, goal_id: str) -> List[ExecutionResult]:
        """
        Get execution history for a goal.
        
        Args:
            goal_id: ID of the goal
            
        Returns:
            List of execution results
        """
        return self.execution_history.get(goal_id, [])
        
    def get_active_goals(self) -> List[Goal]:
        """Get all active goals."""
        return list(self.active_goals.values())
        
    def get_completed_goals(self) -> List[Goal]:
        """Get all completed goals."""
        return list(self.completed_goals.values())
        
    def get_goal_by_id(self, goal_id: str) -> Optional[Goal]:
        """
        Get a goal by its ID.
        
        Args:
            goal_id: ID of the goal
            
        Returns:
            Goal object or None if not found
        """
        if goal_id in self.active_goals:
            return self.active_goals[goal_id]
        elif goal_id in self.completed_goals:
            return self.completed_goals[goal_id]
        return None
        
    def add_goal(self, goal: Goal) -> None:
        """
        Add a new goal to tracking.
        
        Args:
            goal: Goal to add
        """
        self.active_goals[goal.id] = goal
        logger.info(f"Goal added to tracking: {goal.id}")
        self._save_state()
        
    def cancel_goal(self, goal_id: str) -> bool:
        """
        Cancel an active goal.
        
        Args:
            goal_id: ID of the goal to cancel
            
        Returns:
            True if goal was cancelled, False if not found
        """
        if goal_id in self.active_goals:
            goal = self.active_goals[goal_id]
            goal.status = TaskStatus.CANCELLED
            goal.completed_at = datetime.now()
            
            # Cancel all pending tasks
            for task in goal.sub_tasks:
                if task.status == TaskStatus.PENDING:
                    task.status = TaskStatus.CANCELLED
                    
            # Move to completed
            del self.active_goals[goal_id]
            self.completed_goals[goal_id] = goal
            
            logger.info(f"Goal cancelled: {goal_id}")
            self._save_state()
            return True
            
        return False
        
    def get_goal_summary(self) -> Dict[str, Any]:
        """Get a summary of all goals."""
        active_count = len(self.active_goals)
        completed_count = len(self.completed_goals)
        
        # Calculate success rate
        successful_goals = sum(
            1 for goal in self.completed_goals.values()
            if goal.status == TaskStatus.COMPLETED
        )
        success_rate = successful_goals / completed_count if completed_count > 0 else 0.0
        
        # Calculate average execution time
        total_time = sum(
            (goal.completed_at - goal.started_at).total_seconds()
            for goal in self.completed_goals.values()
            if goal.started_at and goal.completed_at
        )
        avg_time = total_time / completed_count if completed_count > 0 else 0.0
        
        return {
            "active_goals": active_count,
            "completed_goals": completed_count,
            "success_rate": success_rate,
            "average_execution_time": avg_time,
            "total_goals": active_count + completed_count
        }
        
    def cleanup_old_goals(self, days: int = 30) -> int:
        """
        Clean up old completed goals.
        
        Args:
            days: Number of days to keep goals
            
        Returns:
            Number of goals cleaned up
        """
        cutoff_date = datetime.now().timestamp() - (days * 24 * 60 * 60)
        
        goals_to_remove = []
        for goal_id, goal in self.completed_goals.items():
            if goal.completed_at and goal.completed_at.timestamp() < cutoff_date:
                goals_to_remove.append(goal_id)
                
        for goal_id in goals_to_remove:
            del self.completed_goals[goal_id]
            if goal_id in self.goal_metrics:
                del self.goal_metrics[goal_id]
            if goal_id in self.execution_history:
                del self.execution_history[goal_id]
                
        if goals_to_remove:
            logger.info(f"Cleaned up {len(goals_to_remove)} old goals")
            self._save_state()
            
        return len(goals_to_remove)
        
    def _update_goal_progress(self, goal: Goal) -> None:
        """Update goal progress based on task completion."""
        if not goal.sub_tasks:
            return
            
        completed_tasks = sum(
            1 for task in goal.sub_tasks
            if task.status == TaskStatus.COMPLETED
        )
        
        progress = completed_tasks / len(goal.sub_tasks)
        goal.progress = progress
        
    def _save_state(self) -> None:
        """Save current state to file."""
        try:
            state_data = {
                "active_goals": {
                    goal_id: goal.to_dict() 
                    for goal_id, goal in self.active_goals.items()
                },
                "completed_goals": {
                    goal_id: goal.to_dict() 
                    for goal_id, goal in self.completed_goals.items()
                },
                "goal_metrics": {
                    goal_id: metrics.to_dict() 
                    for goal_id, metrics in self.goal_metrics.items()
                },
                "last_updated": datetime.now().isoformat()
            }
            
            with open(self.state_file, 'w') as f:
                json.dump(state_data, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Failed to save state: {e}")
            
    def _load_state(self) -> None:
        """Load state from file."""
        try:
            if not self.state_file.exists():
                logger.info("No existing state file found, starting fresh")
                return
                
            with open(self.state_file, 'r') as f:
                state_data = json.load(f)
                
            # Load active goals
            for goal_id, goal_data in state_data.get("active_goals", {}).items():
                try:
                    goal = Goal.from_dict(goal_data)
                    self.active_goals[goal_id] = goal
                except Exception as e:
                    logger.warning(f"Failed to load active goal {goal_id}: {e}")
                    
            # Load completed goals
            for goal_id, goal_data in state_data.get("completed_goals", {}).items():
                try:
                    goal = Goal.from_dict(goal_data)
                    self.completed_goals[goal_id] = goal
                except Exception as e:
                    logger.warning(f"Failed to load completed goal {goal_id}: {e}")
                    
            # Load metrics
            for goal_id, metrics_data in state_data.get("goal_metrics", {}).items():
                try:
                    metrics = GoalMetrics.from_dict(metrics_data)
                    self.goal_metrics[goal_id] = metrics
                except Exception as e:
                    logger.warning(f"Failed to load metrics for goal {goal_id}: {e}")
                    
            logger.info(f"Loaded state: {len(self.active_goals)} active, "
                       f"{len(self.completed_goals)} completed goals")
                       
        except Exception as e:
            logger.error(f"Failed to load state: {e}")
            # Continue with empty state
