"""Enhanced perception system with OCR and UI element detection."""

import cv2
import numpy as np
import hashlib
from typing import List, Optional, Tuple, Dict, Any
from PIL import Image
import io
from loguru import logger

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    logger.warning("EasyOCR not available, falling back to basic text detection")

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    logger.warning("Tesseract not available")

from .data_models import (
    TextElement, UIElement, ScreenState, ChangeDetection
)


class EnhancedPerception:
    """Enhanced perception system for screen analysis."""
    
    def __init__(self, ocr_engine: str = "easyocr", languages: List[str] = None):
        """
        Initialize the enhanced perception system.
        
        Args:
            ocr_engine: OCR engine to use ("easyocr" or "tesseract")
            languages: List of languages for OCR (default: ["en"])
        """
        self.ocr_engine = ocr_engine
        self.languages = languages or ["en"]
        self.previous_screen_hash = None
        self.previous_screen_data = None
        
        # Initialize OCR reader
        self.ocr_reader = None
        if ocr_engine == "easyocr" and EASYOCR_AVAILABLE:
            try:
                self.ocr_reader = easyocr.Reader(self.languages)
                logger.info("EasyOCR initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize EasyOCR: {e}")
                
        # UI element detection templates
        self.ui_templates = self._load_ui_templates()
        
        # Screen change detection threshold
        self.change_threshold = 0.1

    def extract_text_from_screen(self, image_data: bytes) -> List[TextElement]:
        """
        Extract text elements from screen image.
        
        Args:
            image_data: JPEG image data from screen capture
            
        Returns:
            List of detected text elements
        """
        try:
            # Convert bytes to PIL Image
            image = Image.open(io.BytesIO(image_data))
            
            text_elements = []
            
            if self.ocr_engine == "easyocr" and self.ocr_reader:
                text_elements = self._extract_text_easyocr(image)
            elif self.ocr_engine == "tesseract" and TESSERACT_AVAILABLE:
                text_elements = self._extract_text_tesseract(image)
            else:
                logger.warning("No OCR engine available, using basic text detection")
                text_elements = self._extract_text_basic(image)
                
            logger.debug(f"Extracted {len(text_elements)} text elements")
            return text_elements
            
        except Exception as e:
            logger.error(f"Error extracting text from screen: {e}")
            return []

    def detect_ui_elements(self, image_data: bytes) -> List[UIElement]:
        """
        Detect UI elements in screen image.
        
        Args:
            image_data: JPEG image data from screen capture
            
        Returns:
            List of detected UI elements
        """
        try:
            # Convert bytes to OpenCV image
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            ui_elements = []
            
            # Detect buttons
            ui_elements.extend(self._detect_buttons(image))
            
            # Detect text fields
            ui_elements.extend(self._detect_text_fields(image))
            
            # Detect windows
            ui_elements.extend(self._detect_windows(image))
            
            # Detect menus
            ui_elements.extend(self._detect_menus(image))
            
            logger.debug(f"Detected {len(ui_elements)} UI elements")
            return ui_elements
            
        except Exception as e:
            logger.error(f"Error detecting UI elements: {e}")
            return []

    def classify_screen_state(self, image_data: bytes) -> ScreenState:
        """
        Classify the current screen state.
        
        Args:
            image_data: JPEG image data from screen capture
            
        Returns:
            ScreenState object with analysis results
        """
        from datetime import datetime
        
        try:
            # Extract text and UI elements
            text_elements = self.extract_text_from_screen(image_data)
            ui_elements = self.detect_ui_elements(image_data)
            
            # Detect active application
            active_app = self._detect_active_application(text_elements, ui_elements)
            
            # Generate screen hash for change detection
            screen_hash = self._generate_screen_hash(image_data)
            
            screen_state = ScreenState(
                timestamp=datetime.now(),
                ui_elements=ui_elements,
                text_elements=text_elements,
                active_application=active_app,
                screen_hash=screen_hash
            )
            
            logger.debug(f"Classified screen state: {len(text_elements)} text, "
                        f"{len(ui_elements)} UI elements, app: {active_app}")
            return screen_state
            
        except Exception as e:
            logger.error(f"Error classifying screen state: {e}")
            return ScreenState(
                timestamp=datetime.now(),
                ui_elements=[],
                text_elements=[]
            )

    def detect_screen_changes(self, current_data: bytes, previous_data: Optional[bytes] = None) -> ChangeDetection:
        """
        Detect changes between current and previous screen.
        
        Args:
            current_data: Current screen image data
            previous_data: Previous screen image data (optional)
            
        Returns:
            ChangeDetection object with change analysis
        """
        try:
            if previous_data is None:
                previous_data = self.previous_screen_data
                
            if previous_data is None:
                # First capture, no comparison possible
                self.previous_screen_data = current_data
                return ChangeDetection(
                    has_changed=True,
                    change_percentage=1.0,
                    change_type="initial_capture",
                    confidence=1.0
                )
                
            # Convert to OpenCV images
            current_img = self._bytes_to_cv_image(current_data)
            previous_img = self._bytes_to_cv_image(previous_data)
            
            # Resize images to same size if needed
            if current_img.shape != previous_img.shape:
                height, width = min(current_img.shape[0], previous_img.shape[0]), min(current_img.shape[1], previous_img.shape[1])
                current_img = cv2.resize(current_img, (width, height))
                previous_img = cv2.resize(previous_img, (width, height))
                
            # Calculate difference
            diff = cv2.absdiff(current_img, previous_img)
            gray_diff = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
            
            # Calculate change percentage
            total_pixels = gray_diff.shape[0] * gray_diff.shape[1]
            changed_pixels = np.count_nonzero(gray_diff > 30)  # Threshold for significant change
            change_percentage = changed_pixels / total_pixels
            
            # Detect changed regions
            changed_regions = self._find_changed_regions(gray_diff)
            
            # Classify change type
            change_type = self._classify_change_type(changed_regions, change_percentage)
            
            # Update previous screen data
            self.previous_screen_data = current_data
            
            result = ChangeDetection(
                has_changed=change_percentage > self.change_threshold,
                change_percentage=change_percentage,
                changed_regions=changed_regions,
                change_type=change_type,
                confidence=min(change_percentage * 2, 1.0)
            )
            
            logger.debug(f"Screen change detection: {change_percentage:.2%} changed, "
                        f"type: {change_type}")
            return result
            
        except Exception as e:
            logger.error(f"Error detecting screen changes: {e}")
            return ChangeDetection(
                has_changed=False,
                change_percentage=0.0,
                confidence=0.0
            )

    def _extract_text_easyocr(self, image: Image.Image) -> List[TextElement]:
        """Extract text using EasyOCR."""
        try:
            # Convert PIL to numpy array
            img_array = np.array(image)
            
            # Run OCR
            results = self.ocr_reader.readtext(img_array)
            
            text_elements = []
            for (bbox, text, confidence) in results:
                if confidence > 0.5:  # Filter low-confidence results
                    # Convert bbox to coordinates
                    x1, y1 = int(min(point[0] for point in bbox)), int(min(point[1] for point in bbox))
                    x2, y2 = int(max(point[0] for point in bbox)), int(max(point[1] for point in bbox))
                    
                    text_elements.append(TextElement(
                        text=text.strip(),
                        coordinates=(x1, y1, x2 - x1, y2 - y1),
                        confidence=confidence
                    ))
                    
            return text_elements
            
        except Exception as e:
            logger.error(f"EasyOCR text extraction failed: {e}")
            return []

    def _extract_text_tesseract(self, image: Image.Image) -> List[TextElement]:
        """Extract text using Tesseract."""
        try:
            # Get text with bounding boxes
            data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
            
            text_elements = []
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                confidence = int(data['conf'][i])
                
                if text and confidence > 50:  # Filter low-confidence results
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    
                    text_elements.append(TextElement(
                        text=text,
                        coordinates=(x, y, w, h),
                        confidence=confidence / 100.0
                    ))
                    
            return text_elements
            
        except Exception as e:
            logger.error(f"Tesseract text extraction failed: {e}")
            return []

    def _extract_text_basic(self, image: Image.Image) -> List[TextElement]:
        """Basic text detection fallback."""
        # This is a placeholder for basic text detection
        # In a real implementation, this might use simple image processing
        return []

    def _detect_buttons(self, image: np.ndarray) -> List[UIElement]:
        """Detect button-like UI elements."""
        buttons = []
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Detect rectangular shapes that might be buttons
        contours, _ = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            # Filter by area and aspect ratio
            area = cv2.contourArea(contour)
            if 500 < area < 10000:  # Reasonable button size
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h
                
                if 0.5 < aspect_ratio < 4:  # Reasonable button proportions
                    buttons.append(UIElement(
                        element_type="button",
                        text="",  # Would need OCR to extract text
                        coordinates=(x, y, w, h),
                        confidence=0.6
                    ))
                    
        return buttons

    def _detect_text_fields(self, image: np.ndarray) -> List[UIElement]:
        """Detect text field UI elements."""
        # Placeholder implementation
        return []

    def _detect_windows(self, image: np.ndarray) -> List[UIElement]:
        """Detect window UI elements."""
        # Placeholder implementation
        return []

    def _detect_menus(self, image: np.ndarray) -> List[UIElement]:
        """Detect menu UI elements."""
        # Placeholder implementation
        return []

    def _detect_active_application(self, text_elements: List[TextElement], ui_elements: List[UIElement]) -> Optional[str]:
        """Detect the currently active application."""
        # Look for common application indicators in text
        for text_elem in text_elements:
            text_lower = text_elem.text.lower()
            if any(app in text_lower for app in ["chrome", "firefox", "safari", "edge"]):
                return "web_browser"
            elif any(app in text_lower for app in ["word", "notepad", "textedit", "vim", "code"]):
                return "text_editor"
            elif any(app in text_lower for app in ["excel", "calc", "numbers"]):
                return "spreadsheet"
                
        return None

    def _generate_screen_hash(self, image_data: bytes) -> str:
        """Generate a hash for screen change detection."""
        return hashlib.md5(image_data).hexdigest()

    def _bytes_to_cv_image(self, image_data: bytes) -> np.ndarray:
        """Convert bytes to OpenCV image."""
        nparr = np.frombuffer(image_data, np.uint8)
        return cv2.imdecode(nparr, cv2.IMREAD_COLOR)

    def _find_changed_regions(self, diff_image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Find regions that have changed in the difference image."""
        # Find contours of changed areas
        contours, _ = cv2.findContours(diff_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        regions = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 100:  # Filter small changes
                x, y, w, h = cv2.boundingRect(contour)
                regions.append((x, y, w, h))
                
        return regions

    def _classify_change_type(self, changed_regions: List[Tuple[int, int, int, int]], change_percentage: float) -> str:
        """Classify the type of screen change."""
        if change_percentage > 0.5:
            return "application_change"
        elif change_percentage > 0.2:
            return "ui_change"
        elif change_percentage > 0.05:
            return "content_change"
        else:
            return "minor_change"

    def _load_ui_templates(self) -> Dict[str, Any]:
        """Load UI element templates for detection."""
        # Placeholder for UI template loading
        return {}
