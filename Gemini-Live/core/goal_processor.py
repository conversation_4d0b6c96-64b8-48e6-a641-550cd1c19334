"""Goal processing and validation module."""

import re
import json
from typing import List, Dict, Any, Optional
from loguru import logger

from .data_models import (
    Goal, SubTask, SuccessCriteria, ValidationResult, 
    GoalComplexity, TaskStatus
)


class GoalProcessor:
    """Processes and validates natural language goals."""
    
    def __init__(self):
        self.complexity_keywords = {
            GoalComplexity.SIMPLE: [
                "click", "type", "open", "close", "save", "copy", "paste",
                "scroll", "select", "press", "move"
            ],
            GoalComplexity.MEDIUM: [
                "create", "write", "edit", "organize", "find", "search",
                "calculate", "format", "resize", "rename"
            ],
            GoalComplexity.COMPLEX: [
                "research", "analyze", "compare", "summarize", "automate",
                "integrate", "optimize", "configure", "install", "debug"
            ]
        }
        
        self.safety_keywords = {
            "high_risk": [
                "delete", "remove", "uninstall", "format", "reset",
                "shutdown", "restart", "modify system", "change settings"
            ],
            "medium_risk": [
                "install", "download", "upload", "share", "send",
                "publish", "post", "email"
            ]
        }
        
        self.action_patterns = {
            "file_operations": r"(create|open|save|delete|move|copy|rename)\s+(file|folder|document)",
            "text_operations": r"(type|write|edit|input)\s+(.+)",
            "navigation": r"(go to|navigate to|open|visit)\s+(.+)",
            "application": r"(open|start|launch|close)\s+(application|app|program)\s*(.+)?",
            "calculation": r"(calculate|compute|add|subtract|multiply|divide)\s+(.+)",
            "search": r"(search|find|look for)\s+(.+)"
        }

    def parse_goal(self, goal_text: str) -> Goal:
        """
        Parse a natural language goal into a structured Goal object.
        
        Args:
            goal_text: Natural language description of the goal
            
        Returns:
            Goal object with parsed information
        """
        logger.info(f"Parsing goal: {goal_text}")
        
        goal = Goal(description=goal_text.strip())
        
        # Estimate complexity
        goal.complexity = self._estimate_complexity(goal_text)
        
        # Set safety level based on keywords
        goal.safety_level = self._assess_safety_level(goal_text)
        
        # Estimate duration based on complexity
        goal.estimated_duration = self._estimate_duration(goal.complexity, goal_text)
        goal.max_execution_time = min(goal.estimated_duration * 3, 600)  # Max 10 minutes
        
        # Determine if user confirmation is required
        goal.user_confirmation_required = self._requires_confirmation(goal_text)
        
        # Break down into sub-tasks
        goal.sub_tasks = self._break_down_goal(goal_text)
        
        # Define success criteria
        goal.success_criteria = self._define_success_criteria(goal_text)
        
        logger.info(f"Parsed goal: {goal.complexity.value} complexity, {len(goal.sub_tasks)} sub-tasks")
        return goal

    def validate_goal(self, goal: Goal) -> ValidationResult:
        """
        Validate a goal for feasibility and safety.
        
        Args:
            goal: Goal object to validate
            
        Returns:
            ValidationResult with validation details
        """
        logger.info(f"Validating goal: {goal.id}")
        
        result = ValidationResult(is_valid=True, confidence=0.8)
        
        # Check for empty or invalid description
        if not goal.description or len(goal.description.strip()) < 5:
            result.is_valid = False
            result.issues.append("Goal description is too short or empty")
            result.confidence = 0.0
            
        # Check for safety concerns
        safety_issues = self._check_safety_concerns(goal.description)
        if safety_issues:
            result.safety_concerns.extend(safety_issues)
            if goal.safety_level == "high":
                result.warnings.append("High-risk operations detected - user confirmation required")
                
        # Validate sub-tasks
        if not goal.sub_tasks:
            result.warnings.append("No sub-tasks generated - goal may be too vague")
            result.confidence *= 0.7
            
        # Check complexity vs estimated duration
        if goal.complexity == GoalComplexity.COMPLEX and goal.estimated_duration < 60:
            result.warnings.append("Complex goal may need more time than estimated")
            
        # Validate success criteria
        if not goal.success_criteria:
            result.warnings.append("No success criteria defined - completion may be ambiguous")
            result.confidence *= 0.8
            
        result.estimated_complexity = goal.complexity
        result.estimated_duration = goal.estimated_duration
        
        logger.info(f"Goal validation: {'VALID' if result.is_valid else 'INVALID'} "
                   f"(confidence: {result.confidence:.2f})")
        return result

    def _estimate_complexity(self, goal_text: str) -> GoalComplexity:
        """Estimate the complexity of a goal based on keywords and structure."""
        text_lower = goal_text.lower()
        
        # Count complexity indicators
        complex_score = 0
        medium_score = 0
        simple_score = 0
        
        for keyword in self.complexity_keywords[GoalComplexity.COMPLEX]:
            if keyword in text_lower:
                complex_score += 2
                
        for keyword in self.complexity_keywords[GoalComplexity.MEDIUM]:
            if keyword in text_lower:
                medium_score += 1
                
        for keyword in self.complexity_keywords[GoalComplexity.SIMPLE]:
            if keyword in text_lower:
                simple_score += 1
                
        # Check for multiple steps or conditions
        if " and " in text_lower or " then " in text_lower:
            medium_score += 1
            
        # Check for complex patterns
        if re.search(r"(research|analyze|compare|multiple|several|various)", text_lower):
            complex_score += 1
            
        # Determine complexity
        if complex_score >= 2:
            return GoalComplexity.COMPLEX
        elif medium_score >= 2 or complex_score >= 1:
            return GoalComplexity.MEDIUM
        else:
            return GoalComplexity.SIMPLE

    def _assess_safety_level(self, goal_text: str) -> str:
        """Assess the safety level of a goal."""
        text_lower = goal_text.lower()
        
        for keyword in self.safety_keywords["high_risk"]:
            if keyword in text_lower:
                return "high"
                
        for keyword in self.safety_keywords["medium_risk"]:
            if keyword in text_lower:
                return "standard"
                
        return "low"

    def _estimate_duration(self, complexity: GoalComplexity, goal_text: str) -> float:
        """Estimate execution duration based on complexity and content."""
        base_times = {
            GoalComplexity.SIMPLE: 30.0,   # 30 seconds
            GoalComplexity.MEDIUM: 120.0,  # 2 minutes
            GoalComplexity.COMPLEX: 300.0  # 5 minutes
        }
        
        duration = base_times[complexity]
        
        # Adjust based on specific keywords
        text_lower = goal_text.lower()
        if "research" in text_lower or "search" in text_lower:
            duration *= 2
        if "write" in text_lower or "create" in text_lower:
            duration *= 1.5
        if "multiple" in text_lower or "several" in text_lower:
            duration *= 1.3
            
        return min(duration, 600.0)  # Cap at 10 minutes

    def _requires_confirmation(self, goal_text: str) -> bool:
        """Determine if goal requires user confirmation."""
        text_lower = goal_text.lower()
        
        # High-risk operations always require confirmation
        for keyword in self.safety_keywords["high_risk"]:
            if keyword in text_lower:
                return True
                
        # File operations with system directories
        if re.search(r"(system|program files|windows|applications)", text_lower):
            return True
            
        return False

    def _break_down_goal(self, goal_text: str) -> List[SubTask]:
        """Break down a goal into actionable sub-tasks."""
        sub_tasks = []
        
        # Simple pattern-based breakdown
        # This would be enhanced with AI analysis in a full implementation
        
        text_lower = goal_text.lower()
        
        # File operations
        if re.search(self.action_patterns["file_operations"], text_lower):
            match = re.search(self.action_patterns["file_operations"], text_lower)
            action = match.group(1)
            target = match.group(2)
            
            sub_tasks.append(SubTask(
                description=f"Locate target {target}",
                commands=[{"action": "analyze_screen", "purpose": f"find_{target}"}]
            ))
            
            sub_tasks.append(SubTask(
                description=f"{action.title()} {target}",
                commands=[{"action": action, "target": target}],
                dependencies=[sub_tasks[0].id] if sub_tasks else []
            ))
            
        # Application operations
        elif re.search(self.action_patterns["application"], text_lower):
            match = re.search(self.action_patterns["application"], text_lower)
            action = match.group(1)
            app_name = match.group(3) if match.group(3) else "application"
            
            sub_tasks.append(SubTask(
                description=f"{action.title()} {app_name}",
                commands=[{"action": "open_application", "name": app_name}]
            ))
            
        # Text operations
        elif re.search(self.action_patterns["text_operations"], text_lower):
            match = re.search(self.action_patterns["text_operations"], text_lower)
            action = match.group(1)
            content = match.group(2)
            
            sub_tasks.append(SubTask(
                description="Find text input area",
                commands=[{"action": "analyze_screen", "purpose": "find_text_input"}]
            ))
            
            sub_tasks.append(SubTask(
                description=f"{action.title()} content",
                commands=[{"action": "type", "text": content}],
                dependencies=[sub_tasks[0].id] if sub_tasks else []
            ))
            
        # Default: create a single task for simple goals
        if not sub_tasks:
            sub_tasks.append(SubTask(
                description=goal_text,
                commands=[{"action": "analyze_and_execute", "instruction": goal_text}]
            ))
            
        return sub_tasks

    def _define_success_criteria(self, goal_text: str) -> List[SuccessCriteria]:
        """Define success criteria for a goal."""
        criteria = []
        text_lower = goal_text.lower()
        
        # File operations
        if "create" in text_lower and ("file" in text_lower or "document" in text_lower):
            criteria.append(SuccessCriteria(
                description="File created successfully",
                validation_type="file_exists",
                expected_value=True
            ))
            
        # Text input
        if "type" in text_lower or "write" in text_lower:
            criteria.append(SuccessCriteria(
                description="Text appears in target application",
                validation_type="screen_text",
                expected_value="text_present"
            ))
            
        # Application launch
        if "open" in text_lower and ("application" in text_lower or "app" in text_lower):
            criteria.append(SuccessCriteria(
                description="Application is running",
                validation_type="application_state",
                expected_value="running"
            ))
            
        # Default success criteria
        if not criteria:
            criteria.append(SuccessCriteria(
                description="Goal completed without errors",
                validation_type="execution_success",
                expected_value=True
            ))
            
        return criteria

    def _check_safety_concerns(self, goal_text: str) -> List[str]:
        """Check for potential safety concerns in the goal."""
        concerns = []
        text_lower = goal_text.lower()
        
        # High-risk operations
        for keyword in self.safety_keywords["high_risk"]:
            if keyword in text_lower:
                concerns.append(f"High-risk operation detected: {keyword}")
                
        # System file access
        if re.search(r"(system|program files|windows|applications|registry)", text_lower):
            concerns.append("System file or registry access detected")
            
        # Network operations
        if re.search(r"(download|upload|internet|network|email|send)", text_lower):
            concerns.append("Network operation detected")
            
        return concerns
