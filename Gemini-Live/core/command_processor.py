"""
Command processing module for the Gemini Live Computer Control Agent.
Handles interpretation and validation of commands and AI responses.
"""

import re
import json
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from utils.logger import logger


class CommandType(Enum):
    """Types of commands the agent can handle."""
    DIRECT_ACTION = "direct_action"  # Direct action commands
    SCREEN_ANALYSIS = "screen_analysis"  # Analyze screen and suggest actions
    NAVIGATION = "navigation"  # Navigate to specific applications/websites
    TEXT_INPUT = "text_input"  # Text input and editing
    FILE_OPERATION = "file_operation"  # File operations
    SYSTEM_CONTROL = "system_control"  # System-level controls
    CUSTOM = "custom"  # Custom/complex commands


@dataclass
class ProcessedCommand:
    """Represents a processed command with metadata."""
    original_text: str
    command_type: CommandType
    intent: str
    parameters: Dict[str, Any]
    confidence: float
    requires_screen_analysis: bool


class CommandProcessor:
    """Processes and validates user commands and AI responses."""
    
    def __init__(self):
        self.command_patterns = self._initialize_command_patterns()
        
    def _initialize_command_patterns(self) -> Dict[str, Dict]:
        """Initialize command recognition patterns."""
        return {
            # Direct action patterns
            r'click\s+(?:on\s+)?(?:at\s+)?(?:\()?(\d+),?\s*(\d+)(?:\))?': {
                'type': CommandType.DIRECT_ACTION,
                'action': 'click',
                'extract_coords': True
            },
            r'double\s*click\s+(?:on\s+)?(?:at\s+)?(?:\()?(\d+),?\s*(\d+)(?:\))?': {
                'type': CommandType.DIRECT_ACTION,
                'action': 'double_click',
                'extract_coords': True
            },
            r'right\s*click\s+(?:on\s+)?(?:at\s+)?(?:\()?(\d+),?\s*(\d+)(?:\))?': {
                'type': CommandType.DIRECT_ACTION,
                'action': 'right_click',
                'extract_coords': True
            },
            r'move\s+(?:mouse\s+)?(?:to\s+)?(?:\()?(\d+),?\s*(\d+)(?:\))?': {
                'type': CommandType.DIRECT_ACTION,
                'action': 'move',
                'extract_coords': True
            },
            r'type\s+["\'](.+?)["\']': {
                'type': CommandType.TEXT_INPUT,
                'action': 'type',
                'extract_text': True
            },
            r'press\s+(.+)': {
                'type': CommandType.DIRECT_ACTION,
                'action': 'key_press',
                'extract_key': True
            },
            
            # Screen analysis patterns
            r'(?:find|locate|look\s+for)\s+(.+)': {
                'type': CommandType.SCREEN_ANALYSIS,
                'intent': 'find_element'
            },
            r'(?:click\s+on|select)\s+(?:the\s+)?(.+)': {
                'type': CommandType.SCREEN_ANALYSIS,
                'intent': 'click_element'
            },
            r'(?:open|launch|start)\s+(.+)': {
                'type': CommandType.NAVIGATION,
                'intent': 'open_application'
            },
            r'(?:go\s+to|navigate\s+to|visit)\s+(.+)': {
                'type': CommandType.NAVIGATION,
                'intent': 'navigate_to'
            },
            
            # Text input patterns
            r'(?:enter|input|fill\s+in)\s+(.+)': {
                'type': CommandType.TEXT_INPUT,
                'intent': 'enter_text'
            },
            r'(?:search\s+for|search)\s+(.+)': {
                'type': CommandType.TEXT_INPUT,
                'intent': 'search'
            },
            
            # System control patterns
            r'(?:scroll\s+)?(?:up|down)(?:\s+(\d+)\s+times?)?': {
                'type': CommandType.SYSTEM_CONTROL,
                'action': 'scroll'
            },
            r'(?:wait|pause)(?:\s+for\s+)?(\d+(?:\.\d+)?)\s*(?:seconds?)?': {
                'type': CommandType.DIRECT_ACTION,
                'action': 'wait'
            },
        }
    
    def process_user_command(self, command_text: str) -> ProcessedCommand:
        """
        Process a user command and extract intent and parameters.
        
        Args:
            command_text: Raw user command text
            
        Returns:
            ProcessedCommand object with processed information
        """
        command_text = command_text.strip().lower()
        logger.debug(f"Processing command: {command_text}")
        
        # Try to match against known patterns
        for pattern, config in self.command_patterns.items():
            match = re.search(pattern, command_text, re.IGNORECASE)
            if match:
                return self._create_processed_command(command_text, match, config)
        
        # If no pattern matches, treat as custom command requiring screen analysis
        return ProcessedCommand(
            original_text=command_text,
            command_type=CommandType.CUSTOM,
            intent="custom_action",
            parameters={'description': command_text},
            confidence=0.5,
            requires_screen_analysis=True
        )
    
    def _create_processed_command(self, command_text: str, match: re.Match, 
                                config: Dict) -> ProcessedCommand:
        """Create a ProcessedCommand from a regex match."""
        command_type = config['type']
        parameters = {}
        
        # Extract coordinates if specified
        if config.get('extract_coords'):
            try:
                parameters['coordinates'] = [int(match.group(1)), int(match.group(2))]
            except (IndexError, ValueError):
                pass
        
        # Extract text if specified
        if config.get('extract_text'):
            try:
                parameters['text'] = match.group(1)
            except IndexError:
                pass
        
        # Extract key if specified
        if config.get('extract_key'):
            try:
                key_text = match.group(1).strip()
                # Handle key combinations
                if '+' in key_text:
                    parameters['keys'] = [k.strip() for k in key_text.split('+')]
                    parameters['action_type'] = 'key_combination'
                else:
                    parameters['key'] = key_text
                    parameters['action_type'] = 'key_press'
            except IndexError:
                pass
        
        # Set action type if specified
        if 'action' in config:
            parameters['action_type'] = config['action']
        
        # Set intent if specified
        intent = config.get('intent', config.get('action', 'unknown'))
        
        # Determine if screen analysis is required
        requires_screen_analysis = (
            command_type in [CommandType.SCREEN_ANALYSIS, CommandType.NAVIGATION] or
            ('coordinates' not in parameters and command_type == CommandType.DIRECT_ACTION)
        )
        
        return ProcessedCommand(
            original_text=command_text,
            command_type=command_type,
            intent=intent,
            parameters=parameters,
            confidence=0.8,
            requires_screen_analysis=requires_screen_analysis
        )
    
    def validate_ai_response(self, response: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate an AI response for correctness and safety.
        
        Args:
            response: AI response dictionary
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not isinstance(response, dict):
            return False, "Response must be a dictionary"
        
        if 'actions' not in response:
            return False, "Response missing 'actions' field"
        
        actions = response['actions']
        if not isinstance(actions, list):
            return False, "'actions' must be a list"
        
        if len(actions) == 0:
            return False, "No actions specified"
        
        if len(actions) > 10:
            return False, "Too many actions (maximum 10 allowed)"
        
        # Validate each action
        for i, action in enumerate(actions):
            is_valid, error = self._validate_action(action)
            if not is_valid:
                return False, f"Action {i+1}: {error}"
        
        return True, "Response is valid"
    
    def _validate_action(self, action: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate a single action."""
        if not isinstance(action, dict):
            return False, "Action must be a dictionary"
        
        if 'type' not in action:
            return False, "Action missing 'type' field"
        
        action_type = action['type']
        valid_types = [
            'click', 'double_click', 'right_click', 'move', 'drag',
            'scroll', 'type', 'key_press', 'key_combination', 'wait'
        ]
        
        if action_type not in valid_types:
            return False, f"Invalid action type: {action_type}"
        
        # Validate action-specific requirements
        if action_type in ['click', 'double_click', 'right_click', 'move']:
            if 'coordinates' not in action:
                return False, f"{action_type} action requires 'coordinates'"
            
            coords = action['coordinates']
            if not isinstance(coords, list) or len(coords) != 2:
                return False, "Coordinates must be a list of [x, y]"
            
            if not all(isinstance(c, (int, float)) for c in coords):
                return False, "Coordinates must be numeric"
        
        elif action_type == 'drag':
            required_fields = ['start_coordinates', 'end_coordinates']
            for field in required_fields:
                if field not in action:
                    return False, f"Drag action requires '{field}'"
        
        elif action_type == 'type':
            if 'text' not in action:
                return False, "Type action requires 'text'"
            
            text = action['text']
            if not isinstance(text, str):
                return False, "Text must be a string"
            
            if len(text) > 1000:
                return False, "Text too long (maximum 1000 characters)"
        
        elif action_type == 'key_press':
            if 'key' not in action:
                return False, "Key press action requires 'key'"
        
        elif action_type == 'key_combination':
            if 'keys' not in action:
                return False, "Key combination action requires 'keys'"
            
            keys = action['keys']
            if not isinstance(keys, list) or len(keys) < 2:
                return False, "Key combination must have at least 2 keys"
        
        elif action_type == 'wait':
            if 'duration' not in action:
                return False, "Wait action requires 'duration'"
            
            duration = action['duration']
            if not isinstance(duration, (int, float)) or duration <= 0:
                return False, "Duration must be a positive number"
            
            if duration > 30:
                return False, "Wait duration too long (maximum 30 seconds)"
        
        return True, "Action is valid"
    
    def extract_action_sequence(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract and clean action sequence from AI response.
        
        Args:
            response: Validated AI response
            
        Returns:
            List of action dictionaries
        """
        actions = response.get('actions', [])
        cleaned_actions = []
        
        for action in actions:
            # Create a clean action dictionary
            cleaned_action = {
                'type': action['type']
            }
            
            # Copy relevant fields based on action type
            action_type = action['type']
            
            if action_type in ['click', 'double_click', 'right_click', 'move']:
                cleaned_action['coordinates'] = action['coordinates']
            elif action_type == 'drag':
                cleaned_action['start_coordinates'] = action['start_coordinates']
                cleaned_action['end_coordinates'] = action['end_coordinates']
                if 'duration' in action:
                    cleaned_action['duration'] = action['duration']
            elif action_type == 'scroll':
                if 'coordinates' in action:
                    cleaned_action['coordinates'] = action['coordinates']
                if 'clicks' in action:
                    cleaned_action['clicks'] = action['clicks']
                if 'direction' in action:
                    cleaned_action['direction'] = action['direction']
            elif action_type == 'type':
                cleaned_action['text'] = action['text']
                if 'interval' in action:
                    cleaned_action['interval'] = action['interval']
            elif action_type == 'key_press':
                cleaned_action['key'] = action['key']
                if 'presses' in action:
                    cleaned_action['presses'] = action['presses']
            elif action_type == 'key_combination':
                cleaned_action['keys'] = action['keys']
            elif action_type == 'wait':
                cleaned_action['duration'] = action['duration']
            
            # Add optional fields
            if 'button' in action:
                cleaned_action['button'] = action['button']
            
            cleaned_actions.append(cleaned_action)
        
        return cleaned_actions


# Global command processor instance
command_processor = CommandProcessor()
