"""
Gemini Live API client for the Computer Control Agent.
Handles real-time communication with the Gemini Live API.
"""

import asyncio
import json
import websockets
import base64
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
import google.generativeai as genai
from google.generativeai.types import Harm<PERSON>ategory, HarmBlockThreshold

from config.settings import settings
from utils.logger import logger


@dataclass
class Message:
    """Represents a message in the conversation."""
    role: str  # 'user' or 'model'
    content: str
    image_data: Optional[str] = None
    timestamp: Optional[float] = None


class GeminiLiveClient:
    """Client for interacting with Gemini Live API."""
    
    def __init__(self):
        self.api_key = settings.gemini.api_key
        self.model_name = settings.gemini.model_name
        self.conversation_history: List[Message] = []
        self.is_connected = False
        self.websocket = None
        
        # Configure the Gemini API
        genai.configure(api_key=self.api_key)
        
        # Initialize the model
        self.model = genai.GenerativeModel(
            model_name=self.model_name,
            safety_settings={
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
        )
        
        # System prompt for computer control
        self.system_prompt = self._create_system_prompt()
        
    def _create_system_prompt(self) -> str:
        """Create the system prompt for the computer control agent."""
        return """You are an AI assistant that can control a computer through mouse and keyboard actions.

You can perform the following actions:
1. click - Click at specific coordinates
2. double_click - Double-click at coordinates
3. right_click - Right-click at coordinates
4. move - Move mouse to coordinates
5. drag - Drag from start to end coordinates
6. scroll - Scroll up/down at coordinates
7. type - Type text
8. key_press - Press a single key
9. key_combination - Press key combinations (hotkeys)
10. wait - Wait for a specified duration

When responding, provide actions in JSON format like this:
{
  "actions": [
    {
      "type": "click",
      "coordinates": [100, 200],
      "description": "Click on the button"
    },
    {
      "type": "type",
      "text": "Hello World",
      "description": "Type greeting text"
    }
  ],
  "explanation": "I will click the button and then type a greeting."
}

Always analyze the current screen image before suggesting actions. Be precise with coordinates and explain your reasoning. Consider safety and avoid destructive actions.

Current screen information will be provided with each request."""
    
    async def connect(self) -> bool:
        """
        Establish connection to Gemini Live API.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            # For now, we'll use the standard Gemini API
            # The Live API might require different connection setup
            logger.info("Initializing Gemini client...")
            
            # Test the connection with a simple request
            test_response = self.model.generate_content("Hello")
            if test_response:
                self.is_connected = True
                logger.info("Successfully connected to Gemini API")
                return True
            else:
                logger.error("Failed to get response from Gemini API")
                return False
                
        except Exception as e:
            logger.error(f"Failed to connect to Gemini API: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from the API."""
        if self.websocket:
            await self.websocket.close()
        self.is_connected = False
        logger.info("Disconnected from Gemini API")
    
    async def send_screen_analysis_request(self, 
                                         image_base64: str, 
                                         user_command: str,
                                         screen_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Send screen image and user command to Gemini for analysis.
        
        Args:
            image_base64: Base64 encoded screenshot
            user_command: User's natural language command
            screen_info: Additional screen information
            
        Returns:
            Parsed response with actions, or None if failed
        """
        if not self.is_connected:
            logger.error("Not connected to Gemini API")
            return None
        
        try:
            # Prepare the prompt
            prompt = self._create_analysis_prompt(user_command, screen_info)
            
            # Convert base64 to image for Gemini
            image_data = base64.b64decode(image_base64)
            
            # Create the content with image and text
            content = [
                prompt,
                {
                    "mime_type": "image/jpeg",
                    "data": image_data
                }
            ]
            
            logger.info(f"Sending request to Gemini: {user_command[:50]}...")
            
            # Generate response
            response = self.model.generate_content(
                content,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=settings.gemini.max_tokens,
                    temperature=settings.gemini.temperature,
                )
            )
            
            if response and response.text:
                logger.info("Received response from Gemini")
                
                # Parse the response
                parsed_response = self._parse_response(response.text)
                
                # Add to conversation history
                self.conversation_history.append(
                    Message(role="user", content=user_command, image_data=image_base64)
                )
                self.conversation_history.append(
                    Message(role="model", content=response.text)
                )
                
                # Keep conversation history manageable
                if len(self.conversation_history) > 20:
                    self.conversation_history = self.conversation_history[-20:]
                
                return parsed_response
            else:
                logger.error("Empty response from Gemini")
                return None
                
        except Exception as e:
            logger.error(f"Error sending request to Gemini: {e}")
            return None
    
    def _create_analysis_prompt(self, user_command: str, screen_info: Dict[str, Any]) -> str:
        """Create the analysis prompt with context."""
        prompt = f"""
{self.system_prompt}

Current screen information:
- Screen size: {screen_info.get('screen_width', 'unknown')}x{screen_info.get('screen_height', 'unknown')}
- Mouse position: ({screen_info.get('mouse_x', 'unknown')}, {screen_info.get('mouse_y', 'unknown')})

User command: {user_command}

Please analyze the provided screenshot and generate the appropriate actions to fulfill the user's request. 
Respond with a JSON object containing an "actions" array and an "explanation" field.
"""
        return prompt
    
    def _parse_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """
        Parse the Gemini response to extract actions.
        
        Args:
            response_text: Raw response from Gemini
            
        Returns:
            Parsed response dictionary or None if parsing failed
        """
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response_text[start_idx:end_idx]
                parsed = json.loads(json_str)
                
                # Validate the response structure
                if 'actions' in parsed and isinstance(parsed['actions'], list):
                    logger.debug(f"Successfully parsed {len(parsed['actions'])} actions")
                    return parsed
                else:
                    logger.warning("Response missing 'actions' field or invalid format")
                    return None
            else:
                logger.warning("No JSON found in response")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            return None
        except Exception as e:
            logger.error(f"Error parsing response: {e}")
            return None
    
    async def send_text_message(self, message: str) -> Optional[str]:
        """
        Send a text-only message to Gemini.
        
        Args:
            message: Text message to send
            
        Returns:
            Response text or None if failed
        """
        if not self.is_connected:
            logger.error("Not connected to Gemini API")
            return None
        
        try:
            response = self.model.generate_content(message)
            
            if response and response.text:
                # Add to conversation history
                self.conversation_history.append(Message(role="user", content=message))
                self.conversation_history.append(Message(role="model", content=response.text))
                
                return response.text
            else:
                logger.error("Empty response from Gemini")
                return None
                
        except Exception as e:
            logger.error(f"Error sending text message: {e}")
            return None
    
    def get_conversation_history(self) -> List[Message]:
        """Get the conversation history."""
        return self.conversation_history.copy()
    
    def clear_conversation_history(self):
        """Clear the conversation history."""
        self.conversation_history.clear()
        logger.info("Conversation history cleared")


# Global Gemini client instance
gemini_client = GeminiLiveClient()
