"""
Action execution module for the Gemini Live Computer Control Agent.
Handles safe execution of mouse and keyboard actions using pyautogui.
"""

import time
from typing import List, Dict, Any, Optional, Tuple
import pyautogui
from enum import Enum

from config.settings import settings
from utils.logger import logger
from utils.safety import safety_manager


class ActionType(Enum):
    """Enumeration of supported action types."""
    CLICK = "click"
    DOUBLE_CLICK = "double_click"
    RIGHT_CLICK = "right_click"
    MOVE = "move"
    DRAG = "drag"
    SCROLL = "scroll"
    TYPE = "type"
    KEY_PRESS = "key_press"
    KEY_COMBINATION = "key_combination"
    WAIT = "wait"


class ActionExecutor:
    """Executes computer actions safely using pyautogui."""
    
    def __init__(self):
        # Configure pyautogui
        pyautogui.FAILSAFE = True  # Move mouse to corner to abort
        pyautogui.PAUSE = 0.1  # Default pause between actions
        
        # Action execution statistics
        self.actions_executed = 0
        self.actions_failed = 0
        
    def execute_action(self, action: Dict[str, Any]) -> bool:
        """
        Execute a single action with safety checks.
        
        Args:
            action: Dictionary containing action details
            
        Returns:
            True if action was executed successfully, False otherwise
        """
        action_type = action.get('type')
        if not action_type:
            logger.error("Action missing 'type' field")
            return False
        
        try:
            # Convert string to enum
            if isinstance(action_type, str):
                action_type = ActionType(action_type.lower())
        except ValueError:
            logger.error(f"Unknown action type: {action_type}")
            return False
        
        # Extract coordinates if present
        coordinates = action.get('coordinates')
        if coordinates:
            coordinates = tuple(coordinates[:2])  # Ensure it's a tuple of (x, y)
        
        # Safety check
        is_safe, reason = safety_manager.is_action_safe(
            action_type.value, 
            coordinates, 
            action
        )
        
        if not is_safe:
            logger.warning(f"Action blocked by safety check: {reason}")
            return False
        
        # Request confirmation if required
        action_description = self._get_action_description(action)
        if not safety_manager.request_confirmation(action_description):
            logger.info("Action cancelled by user")
            return False
        
        # Execute the action
        try:
            success = self._execute_action_by_type(action_type, action)
            
            if success:
                self.actions_executed += 1
                safety_manager.record_action(action_type.value, coordinates, action)
                logger.info(f"Action executed successfully: {action_description}")
            else:
                self.actions_failed += 1
                logger.error(f"Action execution failed: {action_description}")
            
            return success
            
        except Exception as e:
            self.actions_failed += 1
            logger.error(f"Exception during action execution: {e}")
            return False
    
    def execute_action_sequence(self, actions: List[Dict[str, Any]]) -> bool:
        """
        Execute a sequence of actions.
        
        Args:
            actions: List of action dictionaries
            
        Returns:
            True if all actions executed successfully, False otherwise
        """
        logger.info(f"Executing action sequence with {len(actions)} actions")
        
        for i, action in enumerate(actions):
            logger.debug(f"Executing action {i+1}/{len(actions)}: {action.get('type')}")
            
            if not self.execute_action(action):
                logger.error(f"Action sequence failed at step {i+1}")
                return False
            
            # Small delay between actions
            time.sleep(0.1)
        
        logger.info("Action sequence completed successfully")
        return True
    
    def _execute_action_by_type(self, action_type: ActionType, action: Dict[str, Any]) -> bool:
        """Execute action based on its type."""
        try:
            if action_type == ActionType.CLICK:
                return self._execute_click(action)
            elif action_type == ActionType.DOUBLE_CLICK:
                return self._execute_double_click(action)
            elif action_type == ActionType.RIGHT_CLICK:
                return self._execute_right_click(action)
            elif action_type == ActionType.MOVE:
                return self._execute_move(action)
            elif action_type == ActionType.DRAG:
                return self._execute_drag(action)
            elif action_type == ActionType.SCROLL:
                return self._execute_scroll(action)
            elif action_type == ActionType.TYPE:
                return self._execute_type(action)
            elif action_type == ActionType.KEY_PRESS:
                return self._execute_key_press(action)
            elif action_type == ActionType.KEY_COMBINATION:
                return self._execute_key_combination(action)
            elif action_type == ActionType.WAIT:
                return self._execute_wait(action)
            else:
                logger.error(f"Unsupported action type: {action_type}")
                return False
        except Exception as e:
            logger.error(f"Error executing {action_type.value}: {e}")
            return False
    
    def _execute_click(self, action: Dict[str, Any]) -> bool:
        """Execute a mouse click."""
        coordinates = action.get('coordinates')
        button = action.get('button', 'left')
        
        if coordinates:
            x, y = coordinates
            pyautogui.click(x, y, button=button)
        else:
            pyautogui.click(button=button)
        
        return True
    
    def _execute_double_click(self, action: Dict[str, Any]) -> bool:
        """Execute a mouse double-click."""
        coordinates = action.get('coordinates')
        
        if coordinates:
            x, y = coordinates
            pyautogui.doubleClick(x, y)
        else:
            pyautogui.doubleClick()
        
        return True
    
    def _execute_right_click(self, action: Dict[str, Any]) -> bool:
        """Execute a right mouse click."""
        coordinates = action.get('coordinates')
        
        if coordinates:
            x, y = coordinates
            pyautogui.rightClick(x, y)
        else:
            pyautogui.rightClick()
        
        return True
    
    def _execute_move(self, action: Dict[str, Any]) -> bool:
        """Execute mouse movement."""
        coordinates = action.get('coordinates')
        duration = action.get('duration', 0.5)
        
        if not coordinates:
            logger.error("Move action requires coordinates")
            return False
        
        x, y = coordinates
        pyautogui.moveTo(x, y, duration=duration)
        return True
    
    def _execute_drag(self, action: Dict[str, Any]) -> bool:
        """Execute mouse drag."""
        start_coords = action.get('start_coordinates')
        end_coords = action.get('end_coordinates') or action.get('coordinates')
        duration = action.get('duration', 1.0)
        button = action.get('button', 'left')
        
        if not start_coords or not end_coords:
            logger.error("Drag action requires start_coordinates and end_coordinates")
            return False
        
        start_x, start_y = start_coords
        end_x, end_y = end_coords
        
        pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration, button=button)
        return True
    
    def _execute_scroll(self, action: Dict[str, Any]) -> bool:
        """Execute mouse scroll."""
        coordinates = action.get('coordinates')
        clicks = action.get('clicks', 3)
        direction = action.get('direction', 'up')
        
        if direction == 'down':
            clicks = -abs(clicks)
        else:
            clicks = abs(clicks)
        
        if coordinates:
            x, y = coordinates
            pyautogui.scroll(clicks, x=x, y=y)
        else:
            pyautogui.scroll(clicks)
        
        return True
    
    def _execute_type(self, action: Dict[str, Any]) -> bool:
        """Execute text typing."""
        text = action.get('text', '')
        interval = action.get('interval', 0.01)
        
        if not text:
            logger.error("Type action requires text")
            return False
        
        pyautogui.typewrite(text, interval=interval)
        return True
    
    def _execute_key_press(self, action: Dict[str, Any]) -> bool:
        """Execute single key press."""
        key = action.get('key')
        presses = action.get('presses', 1)
        interval = action.get('interval', 0.1)
        
        if not key:
            logger.error("Key press action requires key")
            return False
        
        pyautogui.press(key, presses=presses, interval=interval)
        return True
    
    def _execute_key_combination(self, action: Dict[str, Any]) -> bool:
        """Execute key combination (hotkey)."""
        keys = action.get('keys', [])
        
        if not keys:
            logger.error("Key combination action requires keys")
            return False
        
        pyautogui.hotkey(*keys)
        return True
    
    def _execute_wait(self, action: Dict[str, Any]) -> bool:
        """Execute wait/delay."""
        duration = action.get('duration', 1.0)
        time.sleep(duration)
        return True
    
    def _get_action_description(self, action: Dict[str, Any]) -> str:
        """Generate human-readable description of an action."""
        action_type = action.get('type', 'unknown')
        coordinates = action.get('coordinates')
        
        description = f"{action_type}"
        
        if coordinates:
            description += f" at ({coordinates[0]}, {coordinates[1]})"
        
        if action_type == 'type':
            text = action.get('text', '')[:20]  # Truncate long text
            description += f" text: '{text}'"
        elif action_type == 'key_press':
            key = action.get('key', '')
            description += f" key: '{key}'"
        elif action_type == 'key_combination':
            keys = action.get('keys', [])
            description += f" keys: {'+'.join(keys)}"
        
        return description
    
    def get_statistics(self) -> Dict[str, int]:
        """Get execution statistics."""
        return {
            'actions_executed': self.actions_executed,
            'actions_failed': self.actions_failed,
            'success_rate': (
                self.actions_executed / (self.actions_executed + self.actions_failed) * 100
                if (self.actions_executed + self.actions_failed) > 0 else 0
            )
        }


# Global action executor instance
action_executor = ActionExecutor()
